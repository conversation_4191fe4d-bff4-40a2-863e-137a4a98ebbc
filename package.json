{"name": "kuber", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "tsc --noEmit && eslint --ext .js,.jsx,.ts,.tsx ./", "start": "react-native start", "test": "jest", "prettier:write": "npx prettier --write **/*.{js,jsx,ts,tsx,json} && npx prettier --write *.{js,jsx,ts,tsx,json}"}, "dependencies": {"@react-native-async-storage/async-storage": "^1.17.11", "@react-native-community/datetimepicker": "^6.7.5", "@react-native-community/netinfo": "^11.3.1", "@react-native-google-signin/google-signin": "^13.1.0", "@react-navigation/drawer": "^6.6.0", "@react-navigation/material-top-tabs": "^6.6.13", "@react-navigation/native": "^6.1.4", "@react-navigation/native-stack": "^6.9.10", "@rneui/base": "^4.0.0-rc.7", "@rneui/themed": "^4.0.0-rc.8", "axios": "^0.24.0", "babel-plugin-transform-remove-console": "^6.9.4", "moment": "^2.29.4", "react": "18.2.0", "react-native": "0.71.4", "react-native-animatable": "^1.4.0", "react-native-calendars": "^1.1294.0", "react-native-date-picker": "^4.2.9", "react-native-device-info": "^10.13.2", "react-native-document-picker": "^9.1.1", "react-native-element-dropdown": "^2.10.1", "react-native-fast-image": "^8.6.3", "react-native-fs": "^2.20.0", "react-native-geolocation-service": "^5.3.1", "react-native-gesture-handler": "^2.9.0", "react-native-get-location": "^3.0.1", "react-native-ionicons": "^4.6.5", "react-native-keyboard-aware-scroll-view": "^0.9.5", "react-native-linear-gradient": "^2.6.2", "react-native-pager-view": "^6.2.3", "react-native-paper": "^5.4.1", "react-native-permissions": "^4.1.5", "react-native-prevent-screenshot-ios-android": "^1.1.0", "react-native-radio-buttons-group": "^2.3.2", "react-native-reanimated": "^2.14.4", "react-native-rename": "^3.2.12", "react-native-render-html": "^6.3.4", "react-native-root-toast": "^3.5.1", "react-native-safe-area-context": "^4.5.0", "react-native-screens": "^3.20.0", "react-native-shadow": "^1.2.2", "react-native-splash-screen": "^3.3.0", "react-native-tab-view": "^3.5.2", "react-native-table-component": "^1.2.2", "react-native-vector-icons": "^9.1.0", "react-native-version-check": "^3.4.7", "react-native-webview": "^13.10.2", "validate.js": "^0.13.1"}, "devDependencies": {"@babel/core": "^7.20.0", "@babel/preset-env": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native-community/eslint-config": "^3.2.0", "@tsconfig/react-native": "^2.0.2", "@types/jest": "^29.2.1", "@types/react": "^18.0.24", "@types/react-native-table-component": "^1.2.4", "@types/react-native-vector-icons": "^6.4.13", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.58.0", "babel-jest": "^29.2.1", "eslint": "^8.38.0", "eslint-config-airbnb-typescript": "^17.0.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-jsx-a11y": "^6.7.1", "eslint-plugin-react": "^7.32.2", "jest": "^29.2.1", "metro-react-native-babel-preset": "0.73.8", "prettier": "^2.8.7", "react-test-renderer": "18.2.0", "typescript": "4.8.4"}, "jest": {"preset": "react-native"}}
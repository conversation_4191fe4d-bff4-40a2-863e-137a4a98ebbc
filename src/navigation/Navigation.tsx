import * as React from 'react';
import {
    NavigationContainer,
    createNavigationContainerRef
} from '@react-navigation/native';
import { createNativeStackNavigator } from '@react-navigation/native-stack';
import {
    createDrawerNavigator,
    DrawerContentScrollView,
    DrawerItemList
} from '@react-navigation/drawer';
import { getFocusedRouteNameFromRoute } from '@react-navigation/native';
import { useRoute } from '@react-navigation/native';

import Login from '../screens/loginscreen/index';
import Food from '../screens/FoodScreen/index';
import History from '../screens/HistoryScreen/index';
import PlaceOrder from '../screens/PlaceOrderScreen/index';
import {
    View,
    Platform,
    Keyboard,
    LayoutAnimation
} from 'react-native';
import RNDrawer from '../component/RNDrawer';
import AsyncStorage from '@react-native-async-storage/async-storage';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Entypo from 'react-native-vector-icons/Entypo';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useEffect, useState } from 'react';
import { httpGet } from '../utils/http';
import styles from './styles';
import { color } from '../utils/constants/color';
import Attendance from '../screens/AttendanceScreen';
import RNText from '../component/RNText';
import RNImage from '../component/RNImage';
import ErrorHandlerPopup from '../component/ErrorHandlerPopup';
import LogoutPopup from '../component/LogOutMessagePopup';
import { stringText } from '../utils/constants/stringsText';
import AttendanceHistoryScreen from '../screens/AttendanceHistoryScreen';
import ViewAttendanceLogsScreen from '../screens/ViewAttendanceLogScreen/index';
import EditServiceRequests from '../screens/EditServiceRequests';
import ServiceRequestsScreenCards from '../screens/ServiceRequestScreens/ServiceRequestsScreenCards/';
import LeavesDetailsScreen from '../screens/LeavesDetails';
import EmplyeeTreeScreen from '../screens/EmplyeeTreeScreen';
import { navigationStringText } from '../utils/constants/navigationStringText';
import DRsOnLeaveScreenCards from '../screens/DRsOnLeaveScreenCards';
import EmployeeInfoScreen from '../screens/EmployeeInfoScreen';
import EmployeeListScreen from '../screens/EmployeeListScreen';
import AssetDetailsScreen from '../screens/AssetDetailsScreen/index';
import BackgroundInfoScreen from '../screens/BackgroundInfoScreen';
import EmployeeInfoView from '../screens/EmployeeInfoScreen/BasicInfoCard';
import EmployeeDetailsScreen from '../screens/EmployeeDetailsScreen';
import BankInfoScreen from '../screens/BankInfoScreen';
import SplashScreenView from '../screens/SplashScreen';
import HolidayListScreen from '../screens/HolidayListScreen';
import AttendanceDetailsScreen from '../screens/DRsAttendanceDetailsScreen';
import DRsDetailsScreen from '../screens/DRsDetailsScreen';
import { enableSecureView, disableSecureView, forbidAndroidShare, allowAndroidShare } from 'react-native-prevent-screenshot-ios-android';
import TimeSheetScreen from '../screens/TimesheetIDSR';
import RCAScreen from '../screens/RCAScreen';
import DocumentScreen from '../screens/DRsDocumentScreen';
import RNButton from '../component/RNButton';
import imageConstant from '../utils/constants/imageConstant';
import apiConstant from '../utils/constants/apiConstant';
import DashBoardCardScreen from '../screens/DashBoard';
import HighLightScreen from '../screens/HighLightScreen';
import YourInfoScreen from '../screens/YourInfoScreen';
import PlanForTheDayScreen from '../screens/PlanForTheDay';
import WorkInfoScreen from '../screens/WorkInfoScreen';
import AddPlanScreen from '../screens/PlanForTheDay/AddPlanForTheDay';
import EditTimesheetScreen from '../screens/TimesheetIDSR/EditTimesheet';


const Stack = createNativeStackNavigator();
const Drawer = createDrawerNavigator();
export const navigationRef = createNavigationContainerRef();

const NavigationDrawer = (props: any) => {
    const { navigation } = props;
    const [namedisplay, setNamedisplay] = useState<string | null>('');
    const [surnamedisplay, setSurnamedisplay] = useState<string | null>();
    const [empdisplay, setEmpisplay] = useState<string | null>();
    const [empImg, setEmpImg] = useState<string>();
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [logoutPopupVisibility, setLogoutPopupVisibility] =
        useState<boolean>(false);
    const [DRSCount, setDRSCount] = useState<number>(0);
    const [employeePortalOption, setEmployeePortalOption] =
        useState<boolean>(false);
    const [DRsOption, setDRsOption] = useState<boolean>(false);
    const [menuOption, setMenuOption] = useState<boolean>(false);

    const logoutButtonClicked = (isVisible: boolean) => {
        setLogoutPopupVisibility(!isVisible);
    };

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        httpGet(apiConstant.USERs)
            .then(async (response: any) => {
                if (response !== null) {
                    setNamedisplay(JSON.parse(response).data.first_name);
                    setSurnamedisplay(JSON.parse(response).data.last_name);
                    setEmpisplay(JSON.parse(response).data.employee_id);

                    if (JSON.parse(response).data.profile_path !== null) {
                        setEmpImg(JSON.parse(response).data.profile_path);
                    }
                    if (JSON.parse(response).data.DRS.count !== null) {
                        setDRSCount(JSON.parse(response).data.DRS.count);
                    }
                }
            })
            .catch((err) => {
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }, [NavigationDrawer]);

    function CustomDrawerContent(props: any) {
        const { navigation } = props;

        const removeData = async () => {
            try {
                await AsyncStorage.removeItem('AuthToken');
                navigation.reset({
                    index: 0,
                    routes: [{ name: navigationStringText.Login }]
                });
            } catch (error) {
                return false;
            }
        };

        const EmployeePortalList = [
            {
                title: navigationStringText.EmployeeInfo,
                icon: imageConstant.EmployeeInfo
            },
            {
                title: navigationStringText.BackgroundInfo,
                icon: imageConstant.BackgroundInfo
            },
            {
                title: navigationStringText.BankInfo,
                icon: imageConstant.BankInfo
            },
            {
                title: navigationStringText.LeavesDetails,
                icon: imageConstant.LeavesIcon
            },
            {
                title: navigationStringText.AssetDetails,
                icon: imageConstant.AssetDetails
            },
            {
                title: navigationStringText.RCA,
                icon: imageConstant.RCA
            },
            // {
            //     title: navigationStringText.PlanForTheDay,
            //     icon: imageConstant.PlanForTheDay
            // },
            // {
            //     title: navigationStringText.IDSR,
            //     icon: imageConstant.IDSR
            // },
            {
                title: "Work Info",
                icon: imageConstant.PlanForTheDay
            },
            {
                title: navigationStringText.AttendanceDetails,
                icon: imageConstant.ATTENDANCE
            },
        ];

        const DrsList = [
            {
                title: navigationStringText.EmployeeTreeDetails,
                icon: imageConstant.DRsIcon
            },
            {
                title: navigationStringText.DRsOnLeaveCards,
                icon: imageConstant.DRSLeave
            }
        ];
        return (
            <View style={styles.mainView}>
                <View style={styles.viewImg}>
                    <View style={styles.centerAlign}>
                        <RNImage source={{ uri: empImg }} style={styles.Img} />
                    </View>
                    <View style={styles.TextView}>
                        <RNText style={styles.textname}>
                            {`${namedisplay} ${surnamedisplay}`}
                        </RNText>
                        <RNText style={styles.textname1}>
                            {stringText.EmpText} {empdisplay}
                        </RNText>
                    </View>
                </View>
                <DrawerContentScrollView
                    {...props}
                    contentContainerStyle={{
                        backgroundColor: color.OFF_WHITE
                    }}
                    showsVerticalScrollIndicator={false}
                >
                    <DrawerItemList {...props} />

                    <RNButton
                        handleOnPress={() => {
                            setEmployeePortalOption(!employeePortalOption);
                            setDRsOption(false);
                        }}>
                        <View style={[styles.employeePortalMainView, { backgroundColor: employeePortalOption ? color.Navigation_Background_Color : color.OFF_WHITE }]}>
                            <View style={styles.employeePortalInnerView}>
                                <RNDrawer
                                    source={imageConstant.EmployeePortal}
                                    style={{
                                        color: employeePortalOption ? color.DARK_BLUE : color.Navigation_Text_Color,
                                        opacity: employeePortalOption ? 1 : 0.5,
                                        paddingVertical: 16,
                                        paddingLeft: 8,
                                        paddingRight: 16
                                    }}
                                />
                                <RNText style={[styles.employeePortalTitleText, {
                                    color: employeePortalOption ? color.DARK_BLUE : color.Navigation_Text_Color,
                                    opacity: employeePortalOption ? 1 : 0.5
                                }]}>
                                    {navigationStringText.EmployeePortal}
                                </RNText>
                            </View>
                            <Ionicons
                                name={employeePortalOption ? 'chevron-up' : 'chevron-down'}
                                size={18}
                                color={employeePortalOption ? color.DARK_BLUE : color.Navigation_Text_Color}
                                style={{
                                    opacity: employeePortalOption ? 1 : 0.5,
                                    marginRight: 20
                                }}
                            />
                        </View>

                        {employeePortalOption == true &&
                            (EmployeePortalList.map((subMenu, index) => (
                                <RNButton key={index}
                                    handleOnPress={() => {
                                        navigation.navigate(subMenu.title);
                                    }}
                                >
                                    <View
                                        style={[styles.employeePortalDropdownView, { backgroundColor: calculateBackGroundColor(subMenu.title) }]}
                                    >
                                        <RNDrawer
                                            source={subMenu.icon}
                                            style={{
                                                opacity: calculateOpacity(
                                                    subMenu.title
                                                ),
                                                color: calculateColor(
                                                    subMenu.title
                                                ),
                                                padding: 16
                                            }}
                                        />
                                        <RNText
                                            style={{
                                                fontSize: 14,
                                                fontFamily: 'Poppins-Regular',
                                                opacity: calculateOpacity(
                                                    subMenu.title
                                                ),
                                                color: calculateColor(
                                                    subMenu.title
                                                )
                                            }}
                                        >
                                            {subMenu.title}
                                        </RNText>
                                    </View>
                                </RNButton>
                            ))
                            )
                        }
                    </RNButton>

                    {DRSCount > 0 && (
                        <>
                            <RNButton
                                handleOnPress={() => {
                                    setDRsOption(!DRsOption);
                                    setEmployeePortalOption(false)
                                }}>
                                <View style={[styles.drsPortalMainView, { backgroundColor: DRsOption ? color.Navigation_Background_Color : color.OFF_WHITE }]}>
                                    <View style={styles.drsPortalInnerView}>
                                        <RNDrawer
                                            source={imageConstant.DRsPortal}
                                            style={{
                                                color: DRsOption ? color.DARK_BLUE : color.Navigation_Text_Color,
                                                opacity: DRsOption ? 1 : 0.5,
                                                paddingVertical: 16,
                                                paddingRight: 16,
                                                paddingLeft: 8
                                            }}
                                        />
                                        <RNText style={{
                                            fontSize: 14,
                                            fontFamily: 'Poppins-Regular',
                                            paddingHorizontal: 15,
                                            color: DRsOption ? color.DARK_BLUE : color.Navigation_Text_Color,
                                            opacity: DRsOption ? 1 : 0.5
                                        }}>
                                            {navigationStringText.DRs}
                                        </RNText>
                                    </View>
                                    <Ionicons
                                        name={DRsOption ? 'chevron-up' : 'chevron-down'}
                                        size={18}
                                        color={DRsOption ? color.DARK_BLUE : color.Navigation_Text_Color}
                                        style={{
                                            opacity: DRsOption ? 1 : 0.5,
                                            marginRight: 20
                                        }}
                                    />
                                </View>

                                {DRsOption == true &&
                                    (DrsList.map((subMenu, index) => (
                                        <RNButton key={index}
                                            handleOnPress={() => {
                                                navigation.navigate(subMenu.title);
                                            }}>
                                            <View style={[styles.drsPortalDropdownView, { backgroundColor: calculateBackGroundColor(subMenu.title) }]}>
                                                <RNDrawer
                                                    source={subMenu.icon}
                                                    style={{
                                                        opacity: calculateOpacity(subMenu.title),
                                                        color: calculateColor(subMenu.title),
                                                        padding: 16
                                                    }}
                                                />
                                                <RNText style={{
                                                    fontSize: 14,
                                                    fontFamily: 'Poppins-Regular',
                                                    opacity: calculateOpacity(subMenu.title),
                                                    color: calculateColor(subMenu.title)
                                                }}>{subMenu.title}</RNText>
                                            </View>
                                        </RNButton>
                                    ))
                                    )
                                }
                            </RNButton>
                        </>
                    )}

                    <View style={[styles.viewList, { backgroundColor: calculateBackGroundColor(navigationStringText.EmployeeList) }]}>
                        <RNButton
                            style={[styles.List, { backgroundColor: calculateBackGroundColor(navigationStringText.EmployeeList) }]}
                            handleOnPress={() => {
                                navigation.navigate(navigationStringText.EmployeeList);
                            }}
                        >
                            <RNDrawer
                                source={imageConstant.EmployeeList}
                                style={{
                                    opacity: calculateOpacity(navigationStringText.EmployeeList),
                                    color: calculateColor(navigationStringText.EmployeeList),
                                    marginRight: 18
                                }}
                            />
                            <RNText style={[styles.drawerText, {
                                opacity: calculateOpacity(navigationStringText.EmployeeList),
                                color: calculateColor(navigationStringText.EmployeeList),
                            }]}>
                                {navigationStringText.EmployeeList}
                            </RNText>
                        </RNButton>
                    </View>

                    <View style={[styles.viewList, { backgroundColor: calculateBackGroundColor(navigationStringText.HolidayList) }]}>
                        <RNButton
                            style={[styles.List, { backgroundColor: calculateBackGroundColor(navigationStringText.HolidayList) }]}
                            handleOnPress={() => {
                                navigation.navigate(navigationStringText.HolidayList);
                            }}
                        >
                            <RNDrawer
                                source={imageConstant.HolidayList}
                                style={{
                                    opacity: calculateOpacity(navigationStringText.HolidayList),
                                    color: calculateColor(navigationStringText.HolidayList),
                                    marginRight: 18
                                }}
                            />
                            <RNText style={[styles.drawerText, {
                                opacity: calculateOpacity(navigationStringText.HolidayList),
                                color: calculateColor(navigationStringText.HolidayList),
                            }]}>
                                {navigationStringText.HolidayList}
                            </RNText>
                        </RNButton>
                    </View>

                    <View style={styles.viewLogOut}>
                        <RNButton
                            style={styles.logOut}
                            handleOnPress={() =>
                                logoutButtonClicked(logoutPopupVisibility)
                            }
                        >
                            <RNImage
                                source={imageConstant.LogOutIcon}
                                style={styles.logoutImage}
                            />
                            <RNText style={styles.logOutText}>
                                {stringText.LogoutText}
                            </RNText>
                        </RNButton>
                    </View>
                </DrawerContentScrollView>
                <ErrorHandlerPopup
                    visible={errorHandlerVisibility}
                    errorHandlerMessage={errorHandlerMessage}
                    errorHandlerClicked={errorHandlerClicked}
                />
                <LogoutPopup
                    visible={logoutPopupVisibility}
                    removeData={removeData}
                    navigation={navigation}
                    setLogoutPopupVisibility={setLogoutPopupVisibility}
                />
            </View>
        );
    }

    const blockScreenShot = () => {
        if (Platform.OS === 'android') {
            forbidAndroidShare();
        }
        if (Platform.OS == 'ios') {
            enableSecureView();
        }
    };

    const allowScreenShot = () => {
        if (Platform.OS === 'android') {
            allowAndroidShare();
        }
        if (Platform.OS == 'ios') {
            disableSecureView();
        }
    };

    const route = useRoute();
    const activeRouteName = getFocusedRouteNameFromRoute(route);

    const calculateOpacity = (routeName: string) => {
        return activeRouteName === routeName ? 1 : 0.5;
    };
    const calculateColor = (routeName: string) => {
        return activeRouteName === routeName
            ? color.DARK_BLUE
            : color.Navigation_Text_Color;
    };
    const calculateBackGroundColor = (routeName: string) => {
        return activeRouteName === routeName
            ? color.Navigation_Background_Color
            : color.OFF_WHITE;
    };

    useEffect(() => {
        if (
            !(
                activeRouteName === navigationStringText.EmployeeInfo ||
                activeRouteName === navigationStringText.BackgroundInfo ||
                activeRouteName === navigationStringText.BankInfo ||
                activeRouteName === navigationStringText.LeavesDetails ||
                activeRouteName === navigationStringText.AssetDetails ||
                activeRouteName === navigationStringText.IDSR ||
                activeRouteName === navigationStringText.PlanForTheDay ||
                activeRouteName === navigationStringText.WorkInfo ||
                activeRouteName === navigationStringText.AttendanceDetails ||
                activeRouteName === navigationStringText.RCA
            )
        ) {
            setEmployeePortalOption(false);
        }
        if (
            !(
                activeRouteName === navigationStringText.EmployeeTreeDetails ||
                activeRouteName === navigationStringText.DRsOnLeaveCards
            )
        ) {
            setDRsOption(false);
        }
        if (
            activeRouteName === navigationStringText.Attendance ||
            activeRouteName === navigationStringText.DinnerOrder
        ) {
            blockScreenShot();
        }
        if (
            !(
                activeRouteName === navigationStringText.Attendance ||
                activeRouteName === navigationStringText.Food
            )
        ) {
            allowScreenShot()
        }
    }, [route]);

    return (
        <Drawer.Navigator
            initialRouteName="DashBoard"
            screenOptions={{
                // unmountOnBlur: true,
                headerShown: true,
                swipeEnabled: true,
                headerStyle: {
                    backgroundColor: color.WHITE
                },
                drawerStyle: {
                    width: 280
                },
                headerTitleAlign: 'left',
                drawerItemStyle: {
                    backgroundColor: color.OFF_WHITE
                },
                headerTintColor: color.DARK_BLUE,
                headerTitleStyle: styles.headerStyles
            }}
            drawerContent={(props) => <CustomDrawerContent {...props} />}
        >
            <Drawer.Screen
                name={navigationStringText.Dashboard}
                component={DashBoardCardScreen}
                options={({ navigation }) => ({
                    unmountOnBlur: true,
                    drawerIcon: () => (
                        <RNDrawer
                            source={imageConstant.Dashboard}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.Dashboard
                                )
                            }}
                        />
                    ),
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    headerShown: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={{
                                marginVertical: 10,
                                marginLeft: 10,
                                marginRight: 20
                            }}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.Dashboard
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ],
                    drawerItemStyle: ({
                        backgroundColor: calculateBackGroundColor(navigationStringText.Dashboard),
                    }),
                })}
            />
            <Drawer.Screen
                name={navigationStringText.Attendance}
                component={AttendanceNavigation}
                options={{
                    unmountOnBlur: true,
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.AttendanceIcon}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.Attendance
                                )
                            }}
                        />
                    ),
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.Attendance
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ],
                    headerShown: false,
                    drawerItemStyle: ({
                        backgroundColor: calculateBackGroundColor(navigationStringText.Attendance),
                    }),
                }}
            />
            <Drawer.Screen
                name={navigationStringText.Food}
                component={FoodNavigation}
                options={{
                    unmountOnBlur: true,
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.Fork}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.Food
                                )
                            }}
                        />
                    ),
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(navigationStringText.Food)
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ],
                    drawerItemStyle: ({
                        backgroundColor: calculateBackGroundColor(navigationStringText.Food),
                    }),
                }}
            />
            <Drawer.Screen
                name={navigationStringText.ServiceRequests}
                component={ServiceRequestsNavigationCardsScreen}
                options={{
                    unmountOnBlur: true,
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.SRIcon}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.ServiceRequests
                                )
                            }}
                        />
                    ),
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.ServiceRequests
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ],
                    drawerItemStyle: ({
                        backgroundColor: calculateBackGroundColor(navigationStringText.ServiceRequests),
                    }),
                }}
            />
            {/* {DRSCount > 0 && (
                <> */}
            <Drawer.Screen
                name={navigationStringText.EmployeeTreeDetails}
                component={EmployeeTreeDetails}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.DRsIcon}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.EmployeeTreeDetails
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.EmployeeTreeDetails),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.EmployeeTreeDetails
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ],
                }}
            />
            {/* </>
            )} */}
            {/* {DRSCount > 0 && (
                <> */}
            <Drawer.Screen
                name={navigationStringText.DRsOnLeaveCards}
                component={DRsOnLeaveCards}
                options={{
                    unmountOnBlur: true,
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.DRSLeave}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.DRsOnLeaveCards
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.DRsOnLeaveCards),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.DRsOnLeaveCards
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
            {/* </>
            )} */}
            <Drawer.Screen
                name={navigationStringText.AssetDetails}
                component={AssetDetails}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.AssetDetails}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.AssetDetails
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.AssetDetails),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.AssetDetails
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
            <Drawer.Screen
                name={navigationStringText.LeavesDetails}
                component={LeaveAccrualReport}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.LeavesIcon}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.LeavesDetails
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.LeavesDetails),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.LeavesDetails
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
            <Drawer.Screen
                name={navigationStringText.EmployeeInfo}
                component={EmployeeInfo}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.EmployeeInfo}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.EmployeeInfo
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.EmployeeInfo),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.EmployeeInfo
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
            <Drawer.Screen
                name={navigationStringText.EmployeeList}
                component={EmployeeList}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.EmployeeList}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.EmployeeList
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.EmployeeList),
                        display: 'none'
                    },
                    unmountOnBlur: true,
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.EmployeeList
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
            <Drawer.Screen
                name={navigationStringText.BackgroundInfo}
                component={BackgroundInfo}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.BackgroundInfo}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.BackgroundInfo
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.BackgroundInfo),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.BackgroundInfo
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
            <Drawer.Screen
                name={navigationStringText.BankInfo}
                component={BankInfo}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.BankInfo}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.BankInfo
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.BankInfo),
                        display: 'none'
                    },
                    unmountOnBlur: true,
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.BankInfo
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
            <Drawer.Screen
                name={navigationStringText.HolidayList}
                component={HolidayList}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.HolidayList}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.HolidayList
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.HolidayList),
                        display: 'none'
                    },
                    unmountOnBlur: true,
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.HolidayList
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
            <Drawer.Screen
                name={navigationStringText.RCA}
                component={RCA}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.LeavesIcon}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.RCA
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.RCA),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.RCA
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
            <Drawer.Screen
                name={navigationStringText.IDSR}
                component={IDSR}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.LeavesIcon}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.IDSR
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.IDSR),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.IDSR
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />

            <Drawer.Screen
                name={navigationStringText.WorkInfo}
                component={WorkInfoNavigation}
                options={({ navigation }) => ({
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.PlanForTheDay}
                            style={{
                                opacity: calculateOpacity(navigationStringText.WorkInfo)
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.WorkInfo),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(navigationStringText.WorkInfo)
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ],
                })}
            />
            <Drawer.Screen
                name={navigationStringText.PlanForTheDay}
                component={PlanForTheDay}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.LeavesIcon}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.PlanForTheDay
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.PlanForTheDay),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.PlanForTheDay
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
            <Drawer.Screen
                name={navigationStringText.AttendanceDetails}
                component={AttendanceDetails}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.LeavesIcon}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.AttendanceDetails
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.AttendanceDetails),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.AttendanceDetails
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
            <Drawer.Screen
                name={navigationStringText.HIGHLIGHT}
                component={HighlightScreen}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.Dashboard}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.HIGHLIGHT
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.HIGHLIGHT),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.HIGHLIGHT
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
            <Drawer.Screen
                name={navigationStringText.YourInfo}
                component={YourInfo}
                options={{
                    drawerIcon: ({ }) => (
                        <RNDrawer
                            source={imageConstant.EmployeeInfo}
                            style={{
                                opacity: calculateOpacity(
                                    navigationStringText.YourInfo
                                )
                            }}
                        />
                    ),
                    drawerItemStyle: {
                        backgroundColor: calculateBackGroundColor(navigationStringText.YourInfo),
                        display: 'none'
                    },
                    headerShown: false,
                    drawerStyle: {
                        backgroundColor: color.OFF_WHITE
                    },
                    drawerActiveTintColor: color.DARK_BLUE,
                    drawerInactiveTintColor: color.Navigation_Text_Color,
                    drawerLabelStyle: [
                        {
                            fontSize: 14,
                            fontFamily: 'Poppins-Regular',
                            opacity: calculateOpacity(
                                navigationStringText.YourInfo
                            )
                        },
                        Platform.OS === 'ios' && { fontWeight: '400' }
                    ]
                }}
            />
        </Drawer.Navigator>
    );
};

const WorkInfoNavigation = (props: any) => {
    const { navigation } = props;
    

    return (
        <Stack.Navigator initialRouteName={navigationStringText.WorkInfo}>
            <Stack.Screen
                name={navigationStringText.WorkInfo}
                component={WorkInfoScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.WorkInfo}
                            </RNText>
                        </View>
                    )
                }}
            />
            <Stack.Screen
                name="EditTimesheetScreen"
                component={EditTimesheetScreen}
                options={{
                    headerShown: true,
                    headerBackVisible: false,
                    headerTitleAlign: 'center',
                    
                    headerTitle: () => (    
                        <View style={[styles.directionRow, { alignItems: 'center', justifyContent: 'center' }]}>
                            <RNText style={[styles.headerStyles, { textAlign: 'center', fontWeight: 'bold' }]}>
                                Edit Timesheet
                            </RNText>
                        </View>
                    ),
                    
                }}
            />

            <Stack.Screen
                name="AddPlanForTheDay"
                component={AddPlanScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,

                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: () => (
                        <View style={[styles.directionRow, { alignItems: 'center', justifyContent: 'center' }]}>
                            <RNText style={[styles.headerStyles, { textAlign: 'center', fontWeight: 'bold' }]}>
                                Create Plan For The Day
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};

const Navigation = (navigation: any) => {
    return (
        <NavigationContainer independent={true} ref={navigationRef}>
            <Stack.Navigator
                initialRouteName={navigationStringText.SplashScreen}
                screenOptions={{
                    orientation: 'portrait'
                }}
            >
                <Stack.Screen
                    name={navigationStringText.SplashScreen}
                    component={SplashScreenView}
                    options={{
                        headerShown: false
                    }}
                />
                <Stack.Screen
                    name={navigationStringText.Login}
                    component={Login}
                    options={{
                        headerShown: false
                    }}
                />
                <Stack.Screen
                    name={navigationStringText.NavigationDrawer}
                    component={NavigationDrawer}
                    options={{ headerShown: false }}
                />
                <Stack.Screen
                    name={navigationStringText.EmployeeDetails}
                    component={EmployeeDetailsScreen}
                    options={{ headerShown: false }}
                />
            </Stack.Navigator>
        </NavigationContainer>
    );
};

const FoodNavigation = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.FoodMenu}
                component={Food}
                options={{
                    headerRight: () => (
                        <RNButton
                            handleOnPress={() =>
                                navigation.navigate(
                                    navigationStringText.History
                                )
                            }
                        >
                            <MaterialCommunityIcons
                                name="history"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.FoodMenu}
                            </RNText>
                        </View>
                    )
                }}
            />
            <Stack.Screen
                name={navigationStringText.History}
                component={History}
                options={{
                    headerShown: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() =>
                                navigation.navigate(
                                    navigationStringText.FoodMenu
                                )
                            }
                            style={styles.rightVerticalMargin}
                        >
                            <Entypo
                                name="chevron-left"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerTitleAlign: 'left',
                    headerStyle: { backgroundColor: color.WHITE },
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.History}
                            </RNText>
                        </View>
                    )
                }}
            />
            <Stack.Screen
                name={navigationStringText.DinnerOrder}
                component={PlaceOrder}
                options={{
                    headerShown: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() =>
                                navigation.navigate(
                                    navigationStringText.FoodMenu
                                )
                            }
                            style={styles.rightVerticalMargin}
                        >
                            <Entypo
                                name="chevron-left"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerTitleAlign: 'left',
                    headerStyle: { backgroundColor: color.WHITE },
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.DinnerOrder}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};

const AttendanceNavigation = (props: any) => {
    const { navigation } = props;
    return (
        <Stack.Navigator initialRouteName={navigationStringText.Attendance}>
            <Stack.Screen
                name={navigationStringText.DailyAttendance}
                component={Attendance}
                options={{
                    headerRight: () => (
                        <RNButton
                            handleOnPress={() =>
                                navigation.replace(
                                    navigationStringText.AttendanceHistory
                                )
                            }
                        >
                            <MaterialCommunityIcons
                                name="history"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerTitleAlign: 'left',
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.DailyAttendance}
                            </RNText>
                        </View>
                    )
                }}
            />
            <Stack.Screen
                name={navigationStringText.AttendanceHistory}
                component={AttendanceHistoryScreen}
                options={{
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() =>
                                navigation.navigate(
                                    navigationStringText.DailyAttendance
                                )
                            }
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="chevron-left"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerTitleAlign: 'left',
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.AttendanceHistory}
                            </RNText>
                        </View>
                    )
                }}
            />
            <Stack.Screen
                name={navigationStringText.AttendanceLogs}
                component={ViewAttendanceLogsScreen}
                options={{
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() =>
                                navigation.navigate(
                                    navigationStringText.AttendanceHistory
                                )
                            }
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="chevron-left"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerTitleAlign: 'left',
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.AttendanceLogs}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const ServiceRequestsNavigationCardsScreen = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator
            initialRouteName={navigationStringText.ServiceRequestsScreenCards}
        >
            <Stack.Screen
                name={navigationStringText.ServiceRequestsScreenCards}
                component={ServiceRequestsScreenCards}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.ServiceRequests}
                            </RNText>
                        </View>
                    )
                }}
            />
            <Stack.Screen
                name={navigationStringText.EditServiceRequests}
                component={EditServiceRequests}
                initialParams={{}}
                options={({ route }) => ({
                    headerShown: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() =>
                                navigation.navigate(
                                    navigationStringText.ServiceRequestsScreenCards
                                )
                            }
                            style={styles.rightVerticalMargin}
                        >
                            <Entypo
                                name="chevron-left"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerTitleAlign: 'left',
                    headerStyle: { backgroundColor: color.WHITE },
                    headerBackVisible: false,
                    headerTitle: () => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {route.params?.srId
                                    ? navigationStringText.EditServiceRequests
                                    : navigationStringText.AddServiceRequests}
                            </RNText>
                        </View>
                    )
                })}
            />
        </Stack.Navigator>
    );
};

const EmployeeTreeDetails = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator
            initialRouteName={navigationStringText.EmployeeTreeDetailsScreen}
        >
            <Stack.Screen
                name={navigationStringText.EmployeeTreeDetailsScreen}
                component={EmplyeeTreeScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.EmployeeTreeDetails}
                            </RNText>
                        </View>
                    )
                }}
            />
            <Stack.Screen
                name={navigationStringText.DRsDeatils}
                component={DRsDetailsScreen}
                initialParams={{ userId: '', name: '' }}
                options={({ route }) => ({
                    headerShown: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() =>
                                navigation.navigate(
                                    navigationStringText.EmployeeTreeDetailsScreen
                                )
                            }
                            style={styles.rightVerticalMargin}
                        >
                            <Entypo
                                name="chevron-left"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerTitleAlign: 'left',
                    headerStyle: { backgroundColor: color.WHITE },
                    headerBackVisible: false,
                    headerTitle: () => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.DRsDeatils}
                            </RNText>
                        </View>
                    )
                })}
            />
            <Stack.Screen
                name={navigationStringText.LeavesDetails}
                component={LeavesDetailsScreen}
                initialParams={{ userId: '', name: '' }}
                options={({ route }) => ({
                    headerShown: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.navigate(
                                    navigationStringText.DRsDeatils, {
                                    userId: route?.params?.userId,
                                    name: route?.params?.name
                                }
                                )
                            }}
                            style={styles.rightVerticalMargin}
                        >
                            <Entypo
                                name="chevron-left"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerTitleAlign: 'left',
                    headerStyle: { backgroundColor: color.WHITE },
                    headerBackVisible: false,
                    headerTitle: () => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {stringText.LeaveAccrualReport}
                            </RNText>
                        </View>
                    )
                })}
            />
            <Stack.Screen
                name={navigationStringText.AttendanceDetails}
                component={AttendanceDetailsScreen}
                initialParams={{ userId: '', name: '' }}
                options={({ route }) => ({
                    headerShown: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                {
                                    navigation.navigate(
                                        navigationStringText.DRsDeatils, {
                                        userId: route?.params?.userId,
                                        name: route?.params?.name
                                    }
                                    )
                                }
                            }}
                            style={styles.rightVerticalMargin}
                        >
                            <Entypo
                                name="chevron-left"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerTitleAlign: 'left',
                    headerStyle: { backgroundColor: color.WHITE },
                    headerBackVisible: false,
                    headerTitle: () => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.AttendanceDetails}
                            </RNText>
                        </View>
                    )
                })}
            />
            <Stack.Screen
                name={navigationStringText.IDSR}
                component={TimeSheetScreen}
                initialParams={{ userId: '', name: '' }}
                options={({ route }) => ({
                    headerShown: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                {
                                    navigation.navigate(
                                        navigationStringText.DRsDeatils, {
                                        userId: route?.params?.userId,
                                        name: route?.params?.name
                                    }
                                    )
                                }
                            }}
                            style={styles.rightVerticalMargin}
                        >
                            <Entypo
                                name="chevron-left"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerTitleAlign: 'left',
                    headerStyle: { backgroundColor: color.WHITE },
                    headerBackVisible: false,
                    headerTitle: () => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.IDSR}
                            </RNText>
                        </View>
                    )
                })}
            />
            <Stack.Screen
                name={navigationStringText.RCA}
                component={RCAScreen}
                initialParams={{ userId: '', name: '' }}
                options={({ route }) => ({
                    headerShown: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                {
                                    navigation.navigate(
                                        navigationStringText.DRsDeatils, {
                                        userId: route?.params?.userId,
                                        name: route?.params?.name
                                    }
                                    )
                                }
                            }}
                            style={styles.rightVerticalMargin}
                        >
                            <Entypo
                                name="chevron-left"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerTitleAlign: 'left',
                    headerStyle: { backgroundColor: color.WHITE },
                    headerBackVisible: false,
                    headerTitle: () => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.RCA}
                            </RNText>
                        </View>
                    )
                })}
            />
            <Stack.Screen
                name={navigationStringText.Document}
                component={DocumentScreen}
                initialParams={{ userId: '', name: '' }}
                options={({ route }) => ({
                    headerShown: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                {
                                    navigation.navigate(
                                        navigationStringText.DRsDeatils, {
                                        userId: route?.params?.userId,
                                        name: route?.params?.name
                                    }
                                    )
                                }
                            }}
                            style={styles.rightVerticalMargin}
                        >
                            <Entypo
                                name="chevron-left"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerTitleAlign: 'left',
                    headerStyle: { backgroundColor: color.WHITE },
                    headerBackVisible: false,
                    headerTitle: () => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.Document}
                            </RNText>
                        </View>
                    )
                })}
            />
        </Stack.Navigator>
    );
};
const DRsOnLeaveCards = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.DRsOnLeaveScreenCards}
                component={DRsOnLeaveScreenCards}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.DRsOnLeaveCards}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const EmployeeInfo = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.EmployeeInfoScreen}
                component={EmployeeInfoScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.EmployeeInfo}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const YourInfo = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.YourInfo}
                component={YourInfoScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.YourInfo}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
}
const EmployeeList = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.EmployeeListScreen}
                component={EmployeeListScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.EmployeeList}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const BankInfo = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.BankInfo}
                component={BankInfoScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.BankInfo}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const BackgroundInfo = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.BackgroundInfoScreen}
                component={BackgroundInfoScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.BackgroundInfo}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const AssetDetails = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.AssetDetailsScreen}
                component={AssetDetailsScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.AssetDetails}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const LeaveAccrualReport = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.LeavesDetails}
                component={LeavesDetailsScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.LeavesDetails}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const IDSR = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.USERIDSR}
                component={TimeSheetScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.USERIDSR}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const PlanForTheDay = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.UserPlanForTheDay}
                component={PlanForTheDayScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.UserPlanForTheDay}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const RCA = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.USERRCA}
                component={RCAScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.USERRCA}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const AttendanceDetails = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.USERAttendanceDetails}
                component={AttendanceDetailsScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.USERAttendanceDetails}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const HolidayList = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.HolidayList}
                component={HolidayListScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {navigationStringText.HolidayList}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};
const HighlightScreen = (props: any) => {
    const { navigation } = props;

    return (
        <Stack.Navigator>
            <Stack.Screen
                name={navigationStringText.HIGHLIGHT}
                component={HighLightScreen}
                options={{
                    headerTitleAlign: 'left',
                    headerShadowVisible: true,
                    headerLeft: () => (
                        <RNButton
                            handleOnPress={() => {
                                navigation.openDrawer(), Keyboard.dismiss();
                            }}
                            style={styles.rightMargin}
                        >
                            <Entypo
                                name="menu"
                                color={color.DARK_BLUE}
                                size={24}
                            />
                        </RNButton>
                    ),
                    headerTitleStyle: styles.headerStyles,
                    headerBackVisible: false,
                    headerTitle: (props) => (
                        <View style={styles.directionRow}>
                            <RNText style={styles.headerStyles}>
                                {new Date().toLocaleString('default', { month: 'short' })} {navigationStringText.HIGHLIGHT}
                            </RNText>
                        </View>
                    )
                }}
            />
        </Stack.Navigator>
    );
};

export default Navigation;

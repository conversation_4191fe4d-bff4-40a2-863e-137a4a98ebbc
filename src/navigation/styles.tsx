import { Platform, StyleSheet } from "react-native";
import { color } from "../utils/constants/color";

const styles = StyleSheet.create({
    mainView: {
        flex: 1,
        height: '100%',
        borderBottomWidth: 1,
        borderBottomColor: color.OFF_WHITE
    },
    Img: {
        height: 56,
        width: 56,
        borderRadius: 100,
        margin: 10,
        backgroundColor: color.BLACK + 10
    },
    viewImg: {
        flexDirection: 'row',
        backgroundColor: color.WHITE,
        height: 100
    },
    TextView: {
        textAlign: 'center',
        marginTop: 20,
        marginLeft: 6,
        width: '70%',
    },
    drawerImg: {
        marginHorizontal: -15
    },
    List: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
    },
    viewList: {
        justifyContent: 'flex-start',
        flexDirection: 'row',
        marginHorizontal: 10,
        paddingVertical: 14,
        paddingHorizontal:8,
        marginVertical:4,
        borderRadius:4
    },
    logOut: {
        flexDirection: 'row',
        alignItems: 'center',
        justifyContent: 'center',
        marginBottom: 20
    },
    drawerText: {
        fontSize: 14,
        fontFamily: 'Poppins-Regular',
        paddingLeft: 16
    },
    logOutText: {
        color: color.DARK_RED,
        fontSize: 14,
        fontFamily: 'Poppins-Regular',
        paddingLeft: 16
    },
    viewLogOut: {
        justifyContent: 'flex-start',
        flexDirection: 'row',
        // marginBottom: 68,
        marginLeft: 16,
        marginTop: 13
    },
    ImglogOut: {
        right: 0,
        left: 30,
        bottom: 45
    },
    textname: {
        color: color.GREY_COLOR,
        fontSize: 14,
        fontFamily: 'Poppins-Regular',
        width: '100%',
    },
    textname1: {
        color: color.GREAYSCALE,
        fontFamily: 'Poppins-Regular',
        fontSize: 12,
        marginTop: 3
    },
    HistoryIcon: {
        marginVertical: 10,
        marginRight: 15
    },
    headerName: {
        fontFamily: 'Poppins-Regular',
    },
    headerStyles: [{
        fontFamily: 'Poppins-Regular',
        color: color.GREY_COLOR,
        fontSize: 18
    }, Platform.OS === 'ios' && {
        fontFamily: 'Poppins-SemiBold',
        fontWeight: '500'
    }],
    employeePortalMainView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginHorizontal:10,
        marginVertical:2,
        borderRadius:4
    },
    employeePortalInnerView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    employeePortalTitleText: {
        fontSize: 14,
        fontFamily: 'Poppins-Regular',
        paddingHorizontal: 15,
    },
    employeePortalDropdownView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: 30,
        marginTop:2,
        marginRight:10,
        borderRadius:4,
        gap: 15
    },
    drsPortalMainView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        marginHorizontal:10,
        marginVertical:2,
        borderRadius:4
    },
    drsPortalInnerView: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    drsPortalDropdownView: {
        flexDirection: 'row',
        alignItems: 'center',
        marginLeft: 30,
        marginRight:10,
        marginTop:2,
        borderRadius:4,
        gap: 15
    },
    logoutImage: {
        height: 24,
        width: 24,
        marginRight: 16
    },
    centerAlign: {
        alignSelf: 'center'
    },
    directionRow: {
        flex: 1,
        flexDirection: 'row',
    },
    rightMargin: {
        marginRight: 24
    },
    rightVerticalMargin: {
        marginVertical: 10,
        marginRight: 20
    }
});

export default styles;
export interface AttendanceHistoryResponse {
    tableData: Array<TableData>,
    tableHead: Array<TableData>,
}

export interface TableData {
    0:  string
    1:  string
    2:  string
    3:  string
}

export interface GenerateAttendanceQrApi {
    success: boolean,
    data: GenerateAttendanceQRResponse
    message: string
}

export interface GenerateAttendanceQRResponse {
    attendance_id: null
    created_at: string
    created_by: string
    date: string
    deleted_at: null
    deleted_by: null
    file_name: string
    id: null
    in_time: null
    latitude: string
    longitude: string
    order_type_id: string
    out_time: null
    updated_at: null
    updated_by: null
    user_id: number
    vendor_latitude: null
    vendor_longitude: null
}

export interface ApiErrorResponseInterface {
    response: ApiErrorResponseObjectInterface
    config?: any,
}

export interface ApiErrorResponseObjectInterface {
    config: any,
    data: any,
    headers: any,
    request: any,
    status: number
    statusText: any
}
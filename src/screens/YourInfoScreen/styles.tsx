import { Platform, StyleSheet } from "react-native";
import { color } from '../../utils/constants/color'


const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 15,
    rowGap: 15
  },
  Info: {
    backgroundColor: color.WHITE,
    padding: 10,
    borderRadius: 5,
    rowGap: 20,
    paddingVertical: 20
  },
  officeLocationInfo: {
    backgroundColor: color.WHITE,
    borderRadius: 5,
    paddingVertical: 20
  },
  label: {
    position: 'absolute',
    backgroundColor: color.WHITE,
    left: 10,
    top: -10,
    zIndex: 999,
    paddingHorizontal: 4,
    fontSize: 14,
    color: color.GREAYSCALE

  },
  infoField: {
    borderWidth: 0.5,
    borderColor: color.GREAYSCALE,
    padding: Platform.OS == 'ios' ? 16 : 10,
    borderRadius: 8,
    color: color.BLACK,
    height: 55,
    verticalAlign: 'middle',
  },
  titleText: {
    color: color.GREAYSCALE,
    paddingLeft: 5
  },
  dropdown: {
    height: 50,
    borderColor: color.GREAYSCALE,
    borderWidth: 0.5,
    borderRadius: 8,
    padding: 15,
  },
  placeholderStyle: {
    fontSize: 16,
    color: color.BLACK
  },
  selectedTextStyle: {
    fontSize: 14,
    color: color.BLACK
  },
  iconStyle: {
    width: 0,
    height: 0,
  },
  AddressTitle: {
    textAlign: 'center',
    margin: 1,
    fontWeight: 'bold',
    paddingBottom: 15,
    fontSize: 16,
    color: color.DARK_BLUE
  },
  tableTitleWrapper: {
    backgroundColor: color.ACCENT_BLUE,
    flexDirection: 'row',
    width: '100%',
    padding: 10
  },
  tableTitleText: {
    color: color.WHITE,
    fontSize: 14,
    fontWeight: Platform.OS == 'ios' ? '500' : '900',
    fontFamily: 'Poppins-Regular',
    width: '33%',
    alignSelf: 'center',
    alignContent: 'center',
    alignItems: 'center',
    justifyContent: 'center',
    textAlign: 'center'
  },
  tableValueTextWrapper: {
    paddingVertical: 8,
    backgroundColor: color.WHITE,
    borderBottomLeftRadius: 6,
    borderBottomRightRadius: 6
  },
  tableValueText: {
    color: color.GREY_COLOR,
    fontSize: 14,
    // fontWeight: Platform.OS == 'ios' ? '500' : '500',
    fontFamily: 'Poppins-Regular',
    width: '33%',
    alignSelf: 'center',
    alignContent: 'center',
    textAlign: 'center'
  },
  profileImage: {
    height: 190,
    width: 190,
    borderRadius: 100,
    alignSelf: 'center',
    backgroundColor: color.BLACK + 10,
  },
  noDataText: {
    paddingLeft: 30,
    paddingVertical: 10,
    fontWeight: 'bold',
    alignSelf: 'center'
  },
  loader: {
    height: '100%',
    width: '100%'
  }
});

export default styles
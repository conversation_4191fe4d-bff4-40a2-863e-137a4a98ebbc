import React, { useEffect, useState } from 'react';
import {
    View,
    TextInput,
    ScrollView,
    Platform,
} from 'react-native';
import { Dropdown } from 'react-native-element-dropdown';
import styles from './style';
import { SafeAreaView } from 'react-native-safe-area-context';
import Spinner from '../../component/Loader';
import RNText from '../../component/RNText';
import { stringText } from '../../utils/constants/stringsText';
import { RadioButton } from 'react-native-paper';
import {
    ServiceDataTypes,
    currentStatusType,
    departmentDropDownType,
    departmentType,
    priorityType,
    requestDropDownType,
    requestType
} from '.';
import RNButton from '../../component/RNButton';
import ServiceRequetsCommentsCard from './ServiceRequetsCommentsCard';
import { color } from '../../utils/constants/color';
import DatePicker from 'react-native-date-picker';
import AddCommentModel from './AddCommentModel';
import RNImage from '../../component/RNImage';
import moment from 'moment';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import RNActivityIndicator from '../../component/Loader';
import DocumentPicker from 'react-native-document-picker';
import RNTextInput from '../../component/RNTextInput';
import { successToast } from '../../component/SuccessToast';
import { SRFieldName } from './FieldNames';
import { PERMISSIONS, request, check, RESULTS } from 'react-native-permissions';
import { httpGet } from '../../utils/http';
import imageConstant from '../../utils/constants/imageConstant';
import apiConstant from '../../utils/constants/apiConstant';

export type Props = {
    isFormValid: () => void;
    formValidation: () => void | boolean;
    navigation: any;
    serviceData: ServiceDataTypes;
    departmentData: departmentType[];
    errorHandlerMessage: string;
    errorHandlerVisibility: boolean;
    errorHandlerClicked: () => void;
    requestTypeData: requestType[];
    handelUpdateServiceData: (
        name: string,
        value: string | number | object
    ) => void;
    timeAgo: (date: Date) => string;
    setServiceData: (value: ServiceDataTypes) => void;
    DeparmentDropDownData: departmentDropDownType[];
    getDepartmentById: (departmentId: string | number) => void;
    department: departmentDropDownType;
    setDepartment: (value: departmentDropDownType) => void;
    requestType: requestDropDownType;
    setRequestType: (value: requestDropDownType) => void;
    requestTypeDataFromDB: requestDropDownType[];
    priority: priorityType;
    priorityData: priorityType[];
    setPriority: (value: priorityType) => void;
    currentStatus: currentStatusType;
    setCurrentStatus: (value: currentStatusType) => void;
    buttons: string[];
    selectedIndex: number;
    setSelectedIndex: (value: number) => void;
    startDateOpen: boolean;
    errors: { [key: string]: string };
    setStartDateOpen: (value: boolean) => void;
    endDateOpen: boolean;
    setEndDateOpen: (value: boolean) => void;
    startDate: Date;
    setStartDate: (value: Date) => void;
    endDate: Date;
    setEndDate: (value: Date) => void;
    statusData: currentStatusType[];
    isAddCommentOpen: boolean;
    setIsAddCommentOpen: (value: boolean) => void;
    comment: string;
    setComment: (value: string) => void;
    serviceDataInput: any;
    setServiceDataInput: (data: any) => void;
    handelSave: (data: any) => void;
    handelPostComment: () => void;
    fetching: boolean;
    setError: ([]) => void;
    selectedFile: any;
    setSelectedFile: (data: any) => void;
};

const EditServiceRequestsView = (props: Props) => {
    const {
        serviceData,
        navigation,
        departmentData,
        DeparmentDropDownData,
        requestTypeData,
        errorHandlerClicked,
        errorHandlerMessage,
        errorHandlerVisibility,
        getDepartmentById,
        errors,
        handelUpdateServiceData,
        setDepartment,
        setRequestType,
        requestTypeDataFromDB,
        setServiceData,
        timeAgo,
        department,
        requestType,
        priority,
        priorityData,
        setPriority,
        buttons,
        selectedIndex,
        currentStatus,
        setCurrentStatus,
        setSelectedIndex,
        startDateOpen,
        setStartDateOpen,
        endDateOpen,
        setEndDateOpen,
        startDate,
        setStartDate,
        endDate,
        setEndDate,
        statusData,
        isAddCommentOpen,
        setIsAddCommentOpen,
        comment,
        setComment,
        serviceDataInput,
        setServiceDataInput,
        handelSave,
        handelPostComment,
        fetching,
        setError,
        selectedFile,
        setSelectedFile
    } = props;

    const askForPermission = (permission: any) => {
        request(permission).then(result => {
            if (result === 'granted') {
                selectDoc();
            } else if (result === 'blocked'){
                successToast("Access blocked")
            } else if (result === 'denied'){
                successToast("Permission denied")
            } else if (result === 'unavailable'){
                selectDoc();
            }
        }).catch(error => {
            console.log("Error requesting permission:", error);
        });
    };

    const selectDoc = async () => {
        try {
            const doc = await DocumentPicker.pick({
                type: [
                    DocumentPicker.types.images,
                    DocumentPicker.types.pdf,
                    DocumentPicker.types.doc,
                    DocumentPicker.types.docx
                ]
            });
            if (doc) {
                props.formValidation(SRFieldName.File, doc);
                if (doc[0] && doc[0].size && doc[0].size > 1024 * 1024) {
                    successToast("File size should be less than 1 MB.")
                }
                else {
                    const modifiedDoc = {
                        uri: doc[0].uri,
                        type: doc[0].type,
                        name: doc[0].name
                    };
                    setSelectedFile(modifiedDoc);
                    serviceData?.id
                        ? setServiceData({
                            ...serviceData,
                            file: modifiedDoc,
                            file_paths: doc[0].name
                        })
                        : setServiceDataInput({
                            ...serviceDataInput,
                            file: modifiedDoc,
                            file_paths: doc[0].name
                        });
                }
            }
        } catch (err) {
            if (DocumentPicker.isCancel(err))
                console.log('User cancelled the upload', err);
            else console.log(err);
        }
    };
    const [Fetching, setFetching] = useState(fetching);

    const [checked, setChecked] = React.useState('self');
    useEffect(() => {
        if (serviceData) {
            if (serviceData.id) {
                setChecked(
                    serviceData.id
                        ? serviceData.leaveFor == 2
                            ? 'drs'
                            : 'self'
                        : 'self'
                );
                if (serviceData.leaveFor == 2) {
                    handelChangeStatusToDRs();
                }
            }
        }
    }, [serviceData]);
    const handleChange = (item: any) => {
        setPriority(item);
        setServiceDataInput({
            ...serviceDataInput,
            priority: item.value
        });
    };

    const handelChangeStatusToSelf = () => {
        setServiceDataInput({ ...serviceDataInput, leaveFor: 1 });
        setChecked('self');
    };
    
    const [test, setTest ] = useState<boolean>(false);
    useEffect(()=>{
        httpGet(apiConstant.DRs_LIST)
            .then((response: any) => {
                const drData = JSON.parse(response)?.data;                
                if (serviceData.id !== null) {
        setServiceDataInput({ ...serviceDataInput, leaveFor: 0 });
        setChecked('drs');
    }
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked();
                } else {
                    errorHandlerClicked();
                }
            });
    },[test])
    const handelChangeStatusToDRs = () => {
        setTest(!test);
    };
    const formatDate = (inputDate: string) => {
        const date = new Date(inputDate);
        const day = date.getUTCDate();
        const month = date.getUTCMonth() + 1;
        const year = date.getUTCFullYear();
        const formattedDay = day < 10 ? `0${day}` : day;
        const formattedMonth = month < 10 ? `0${month}` : month;
        const formattedDate = `${formattedDay}/${formattedMonth}/${year}`;
        return formattedDate;
    }
    // const { isLoading, error, drsEmployeeData } = useSelector(dRSEmployeeData);
    const [drsList, setDrsList] = useState<any>([]);

    const [drsEmpSelected, setEmpdrsSelected] = useState({
        label: '',
        value: ''
    });

    const [employeeError, SetEmployeeError] = useState<boolean>(false);
    const [projecGroupError, setProjectGroupError] = useState<boolean>(false);
    useEffect(() => {
        httpGet(apiConstant.DRs_LIST)
            .then((response: any) => {
                const drsEmployeeData = JSON.parse(response)?.data;

        if (drsEmployeeData) {            
            setDrsList(
                drsEmployeeData?.DRs?.map((item: any) => ({
                    label: item.employeeName,
                    value: item.userId
                }))
            );
            }
            if (serviceData.leaveDates) {
                drsEmployeeData?.DRs?.map((item: any) => {
                    if (item.userId === serviceData.leaveDates.id_employee) {
                        setEmpdrsSelected({
                            label: item.employeeName,
                            value: item.userId
                        });
                    }
                });
            }
        })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked();
                } else {
                    errorHandlerClicked();
                }
            });
    }, [checked]);

    const handelChangeDREmployee = (item: any) => {
        if (checked == 'drs') {
            setEmpdrsSelected(item);
            setServiceDataInput({
                ...serviceDataInput,
                employeeId: item.value,
                leaveFor: 2
            });
        }
    };

    useEffect(() => {
        if (checked !== 'drs') {
            setServiceDataInput({
                ...serviceDataInput,
                employeeId: '',
                leaveFor: 1
            });
        }
    }, [checked]);

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <ScrollView contentContainerStyle={styles.scrollContainer}>
                <View style={styles.parentContainer}>
                    <RNText
                        style={[
                            styles.titleText,
                            {
                                textAlign: 'center',
                                marginVertical: 10
                            }
                        ]}
                    >
                        {serviceData?.srId
                            ? serviceData?.srId
                            : 'Create New Service Request'}
                    </RNText>
                    <View style={{ gap: 4 }}>
                        <RNText style={styles.departmentTxt}>
                            {stringText.Department}
                            <RNText style={styles.importantSign}>*</RNText>
                        </RNText>
                        <Dropdown
                            style={[
                                styles.dropDown,
                                { opacity: serviceData.id ? 0.5 : 1 }
                            ]}
                            data={DeparmentDropDownData}
                            placeholderStyle={{ color: color.BLACK }}
                            itemTextStyle={{ color: color.BLACK }}
                            selectedTextStyle={{ color: color.BLACK }}
                            maxHeight={600}
                            labelField="label"
                            valueField="value"
                            placeholder={stringText.SelectDepartment}
                            value={
                                department.label
                                    ? {
                                        label: department.label,
                                        value: department.value
                                    }
                                    : DeparmentDropDownData[0]
                            }
                            disable={serviceData.id ? true : false}
                            // onFocus={() => setIsFocus(true)}
                            onChange={(item) => {
                                setDepartment(item);
                                getDepartmentById(item.value.id);

                                serviceData?.id
                                    ? setServiceData({
                                        ...serviceData,
                                        department: {
                                            id: +item.value.id,
                                            dept_code: item.value.dept_code,
                                            dept_name: item.value.dept_name
                                        }
                                    })
                                    : setServiceDataInput({
                                        ...serviceDataInput,
                                        department: item.value,
                                        issues: ''
                                    });
                            }}
                        />
                        {errors.department && (
                            <RNText style={styles.errorText}>
                                {errors.department}
                            </RNText>
                        )}
                    </View>
                    <View style={{ gap: 4 }}>
                        <RNText style={styles.requestTypeText}>
                            {stringText.RequestType}
                            <RNText style={styles.importantSign}>*</RNText>
                        </RNText>
                        <Dropdown
                            style={[
                                styles.dropDown,
                                { opacity: serviceData.id ? 0.5 : 1 }
                            ]}
                            data={requestTypeDataFromDB}
                            placeholderStyle={{ color: color.BLACK }}
                            itemTextStyle={{ color: color.BLACK, }}
                            selectedTextStyle={{ color: color.BLACK }}
                            selectedTextProps={{ numberOfLines: 1 }}
                            maxHeight={600}
                            labelField="label"
                            valueField="value"
                            placeholder={stringText.SelectType}
                            value={requestType}
                            disable={serviceData.id ? true : false}
                            onChange={(item: any) => {
                                setRequestType({
                                    label: item.label,
                                    value: item.value
                                });
                                serviceData.id
                                    ? setServiceData({
                                        ...serviceData,
                                        issues: item.value
                                    })
                                    : setServiceDataInput({
                                        ...serviceDataInput,
                                        issues: item.value
                                    });
                                props.formValidation(SRFieldName.Issue, item);
                                if (
                                    item?.value?.id == '80' &&
                                    department?.value?.id == '3'
                                ) {
                                    // dispatch(
                                    //     fetchDRSEmployeeData(
                                    //         `users/drs-list`
                                    //     ) as any
                                    // );
                                    handelChangeStatusToDRs()
                                }
                            }}
                        />
                        {errors.issues && (
                            <RNText style={styles.errorText}>
                                {errors.issues}
                            </RNText>
                        )}
                    </View>

                    {
                    requestType?.value?.id == '80' &&
                        department?.value?.id == '3' &&
                        drsList.length > 0 ? (
                        <>
                            <View
                                style={{
                                    gap: 4
                                }}
                            >
                                <RNText style={styles.descriptionText}>
                                    {stringText.LeaveFor}
                                    <RNText style={styles.importantSign}>
                                        *
                                    </RNText>
                                </RNText>
                                <View style={{ flexDirection: 'column' }}>
                                    <RNButton
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            opacity: serviceData.id ? 0.4 : 1
                                        }}
                                        handleOnPress={() =>
                                            serviceData.id
                                                ? ''
                                                : handelChangeStatusToSelf()
                                        }
                                    >
                                        <RadioButton.Item
                                            mode="android"
                                            style={{
                                                marginLeft: -15,
                                                marginBottom: -5
                                            }}
                                            position="leading"
                                            label="Self"
                                            color={color.BLACK}
                                            value="self"
                                            onPress={() =>
                                                serviceData.id
                                                    ? ''
                                                    : setChecked('self')
                                            }
                                            status={
                                                checked === 'self'
                                                    ? 'checked'
                                                    : 'unchecked'
                                            }
                                        />
                                    </RNButton>
                                    <RNButton
                                        handleOnPress={() => {
                                            if (!serviceData.id) {
                                                SetEmployeeError(false);
                                                setChecked('drs');
                                                handelChangeStatusToDRs();
                                            }
                                        }}
                                        style={{
                                            flexDirection: 'row',
                                            alignItems: 'center',
                                            opacity: serviceData.id ? 0.4 : 1
                                        }}
                                    >
                                        <RadioButton.Item
                                            mode="android"
                                            style={{ marginLeft: -15 }}
                                            position="leading"
                                            label="DRs"
                                            color={color.BLACK}
                                            value="drs"
                                            onPress={() => {
                                                if (!serviceData.id) {
                                                    SetEmployeeError(false);
                                                    setChecked('drs');
                                                    handelChangeStatusToDRs();
                                                }
                                            }}
                                            status={
                                                checked === 'drs'
                                                    ? 'checked'
                                                    : 'unchecked'
                                            }
                                        />
                                    </RNButton>
                                </View>

                                {fetching ? (
                                    <RNActivityIndicator
                                        animating={fetching}
                                    />
                                ) : (
                                    checked === 'drs' &&
                                    drsList && (
                                        <>

                                            <RNText style={styles.titleText}>
                                                DR's List
                                                <RNText style={styles.importantSign}>*</RNText>
                                            </RNText>


                                            <Dropdown
                                                style={[
                                                    styles.dropDown,
                                                    {
                                                        opacity: serviceData.id
                                                            ? 0.5
                                                            : 1
                                                    }
                                                ]}
                                                data={drsList}
                                                placeholderStyle={{
                                                    color: color.BLACK
                                                }}
                                                itemTextStyle={{
                                                    color: color.BLACK
                                                }}
                                                selectedTextStyle={{
                                                    color: color.BLACK
                                                }}
                                                maxHeight={600}
                                                labelField="label"
                                                valueField="value"
                                                placeholder={
                                                    stringText.SelectEmployee
                                                }
                                                value={drsEmpSelected}
                                                disable={
                                                    serviceData.id
                                                        ? true
                                                        : false
                                                }
                                                // onFocus={() => setIsFocus(true)}
                                                onChange={(item) => {
                                                    SetEmployeeError(false);
                                                    handelChangeDREmployee(
                                                        item
                                                    );
                                                }}
                                            />
                                            {employeeError === true && (
                                                <RNText
                                                    style={styles.errorText}
                                                >
                                                    {errors.DRName}
                                                    Please Select Employee
                                                </RNText>
                                            )}
                                        </>
                                    )
                                )}
                            </View>
                        </>
                    ) : null
                    }

                    {requestType?.value?.id == '80' &&
                        department?.value?.id == '3' ? (
                        <>
                            <View>
                                <View style={styles.ButtonImg}>
                                    <View style={styles.datePickerLableWrapper}>
                                        <RNText style={styles.datePickerLable}>
                                            {stringText.StartDate}
                                            <RNText
                                                style={styles.importantSign}
                                            >
                                                *
                                            </RNText>
                                        </RNText>
                                        <RNButton
                                            handleOnPress={() => {
                                                serviceData.id
                                                    ? ''
                                                    : setStartDateOpen(true);
                                            }}
                                            style={[
                                                styles.datePickerWrapper,
                                                {
                                                    opacity: serviceData.id
                                                        ? 0.4
                                                        : 1
                                                }
                                            ]}
                                        >
                                            <RNText
                                                children={`${moment(
                                                    serviceData.leaveDates
                                                        ?.leave_start_date
                                                        ? serviceData.leaveDates
                                                            ?.leave_start_date
                                                        : startDate
                                                ).format('DD/MM/YYYY')}`}
                                                style={[
                                                    styles.inputText,
                                                    {
                                                        borderColor:
                                                            errors.startLeaveDates
                                                                ? color.DARK_RED
                                                                : color.GREY_COLOR,
                                                        color: errors.startLeaveDates
                                                            ? color.DARK_RED
                                                            : color.GREY_COLOR
                                                    }
                                                ]}
                                            />
                                            <RNImage
                                                source={imageConstant.Calender}
                                                style={styles.calenderView}
                                            />
                                        </RNButton>
                                    </View>
                                    <View style={styles.datePickerLableWrapper}>
                                        <RNText style={styles.datePickerLable}>
                                            {stringText.EndDate}
                                            <RNText
                                                style={styles.importantSign}
                                            >
                                                *
                                            </RNText>
                                        </RNText>
                                        <RNButton
                                            handleOnPress={() => {
                                                serviceData.id
                                                    ? ''
                                                    : setEndDateOpen(true)
                                            }}
                                            style={[
                                                styles.datePickerWrapper,
                                                {
                                                    opacity: serviceData.id
                                                        ? 0.4
                                                        : 1
                                                }
                                            ]}
                                        >
                                            <RNText
                                                children={`${moment(
                                                    serviceData.leaveDates
                                                        ?.leave_end_date
                                                        ? serviceData.leaveDates
                                                            ?.leave_end_date
                                                        : endDate
                                                ).format('DD/MM/YYYY')}`}
                                                style={[
                                                    styles.inputText,
                                                    {
                                                        borderColor:
                                                            errors.startLeaveDates
                                                                ? color.DARK_RED
                                                                : color.GREY_COLOR,
                                                        color: errors.startLeaveDates
                                                            ? color.DARK_RED
                                                            : color.GREY_COLOR
                                                    }
                                                ]}
                                            />
                                            <RNImage
                                                source={imageConstant.Calender}
                                                style={styles.calenderView}
                                            />
                                        </RNButton>
                                    </View>
                                </View>

                                {errors.startLeaveDates && (
                                    <RNText style={styles.errorText}>
                                        {errors.startLeaveDates}
                                    </RNText>
                                )}

                                <DatePicker
                                    modal
                                    open={startDateOpen}
                                    date={startDate}
                                    onConfirm={(date) => {
                                        setStartDateOpen(false);
                                        setStartDate(date);
                                         props.formValidation(SRFieldName.StartLeaveDates, date)
                                        // setServiceDataInput({
                                        //     ...serviceDataInput,
                                        //     leaveDates: {
                                        //         ...serviceDataInput.leaveDates,
                                        //         leave_start_date:date
                                        //     }
                                        // });
                                    }}
                                    onCancel={() => {
                                        setStartDateOpen(false);
                                    }}
                                    mode="date"
                                />
                                <DatePicker
                                    modal
                                    open={endDateOpen}
                                    date={endDate}
                                    onConfirm={(date) => {
                                        setEndDateOpen(false);
                                        setEndDate(date);
                                         props.formValidation(SRFieldName.EndLeaveDates, date)
                                         // setServiceDataInput({
                                        //     ...serviceDataInput,
                                        //     leaveDates: {
                                        //         ...serviceDataInput.leaveDates,
                                        //         leave_end_date:date
                                        //     }
                                        // });
                                    }}
                                    onCancel={() => {
                                        setEndDateOpen(false);
                                    }}
                                    mode="date"
                                />
                            </View>
                        </>
                    ) : null}

                    <View style={{ gap: 4 }}>
                        <RNText style={styles.titleText}>
                            {stringText.Title}
                            <RNText style={styles.importantSign}>*</RNText>
                        </RNText>
                        <TextInput
                            style={[
                                styles.titleTextInput,
                                {
                                    opacity: serviceData.title ? 0.5 : 1,
                                    minHeight: 40,
                                    paddingTop: 10
                                }
                            ]}
                            placeholder={stringText.EnterTitle}
                            multiline={true}
                            editable={serviceData.title ? false : true}
                            value={
                                serviceData.id
                                    ? serviceData.title
                                    : serviceDataInput.title
                            }
                            onChangeText={(e) => {

                                serviceData.id
                                    ? setServiceData({
                                        ...serviceData,
                                        title: e
                                    })
                                    : setServiceDataInput({
                                        ...serviceDataInput,
                                        title: e
                                    });
                                props.formValidation(SRFieldName.Title, e)
                            }}
                        />
                        {errors.title && (
                            <RNText style={styles.errorText}>
                                {errors.title}
                            </RNText>
                        )}
                    </View>
                    <View style={{ gap: 4 }}>
                        <RNText style={styles.descriptionText}>
                            {stringText.Description}
                            <RNText style={styles.importantSign}>*</RNText>
                        </RNText>
                        <RNTextInput
                            style={[
                                styles.descriptionTextInput,
                                {
                                    opacity: serviceData.description ? 0.5 : 1,
                                    minHeight: 40,
                                    paddingTop: 10
                                }
                            ]}
                            placeHolder={stringText.EnterShortDescription}
                            isMultiline={true}
                            handelOnChange={(e) => {
                                serviceData.id
                                    ? setServiceData({
                                        ...serviceData,
                                        description: e
                                    })
                                    : setServiceDataInput({
                                        ...serviceDataInput,
                                        description: e
                                    });
                                props.formValidation(SRFieldName.Description, e)
                            }}
                            editable={serviceData.description ? false : true}
                            inputValue={
                                serviceData.id
                                    ? serviceData.description
                                    : serviceDataInput.description
                            }
                        />
                        {errors.description && (
                            <RNText style={styles.errorText}>
                                {errors.description}
                            </RNText>
                        )}
                    </View>
                    {serviceData?.id && (
                        <>
                            <View >
                                <RNText style={styles.projectText}>
                                    Created Date
                                    <RNText style={styles.importantSign}>
                                        *
                                    </RNText>

                                </RNText>
                                <RNText style={[
                                    styles.descriptionTextInput,
                                    {
                                        opacity: serviceData.description ? 0.5 : 1,
                                        minHeight: 40,
                                        paddingTop: 10
                                    }
                                ]}>{formatDate(serviceData?.created_at)}</RNText>
                            </View>
                        </>
                    )}
                    {requestType?.value?.id == '80' &&
                        department?.value?.id == '3' ? (
                        <>
                            <View>
                                <RNText style={styles.projectText}>
                                    {stringText.ProjectGroup}
                                    <RNText style={styles.importantSign}>
                                        *
                                    </RNText>
                                </RNText>
                                <TextInput
                                    keyboardType="email-address"
                                    style={[
                                        styles.projectTextInput,
                                        {
                                            opacity: serviceData.project_group
                                                ? 0.5
                                                : 1,
                                            minHeight: 40,
                                            paddingTop: 10
                                        }
                                    ]}
                                    placeholder={stringText.ProjectGroupEmail}
                                    multiline={true}
                                    onChangeText={(e) => {
                                        serviceData.id
                                            ? setServiceData({
                                                ...serviceData,
                                                project_group: e
                                            })
                                            : setServiceDataInput({
                                                ...serviceDataInput,
                                                project_group: e
                                            });
                                        props.formValidation(SRFieldName.Project_group, e)
                                    }}
                                    editable={serviceData.id ? false : true}
                                    value={
                                        serviceData.id
                                            ? serviceData.project_group
                                            : serviceDataInput.project_group
                                    }
                                />
                                {errors.project_group && (
                                    <RNText style={styles.errorText}>
                                        {errors.project_group}
                                    </RNText>
                                )}
                            </View>
                        </>
                    ) : null}
                    {projecGroupError === true && (
                        <RNText style={styles.errorText}>
                            Please enter project group comma-separated.
                        </RNText>
                    )}
                    {requestType?.value?.id == 34 &&
                        department?.value.id == 4 ? (
                        <>
                            <View>
                                <RNText style={styles.projectText}>
                                    {stringText.Amount}
                                    <RNText style={styles.importantSign}>
                                        *
                                    </RNText>
                                </RNText>
                                <RNTextInput
                                    style={[
                                        styles.titleTextInput,
                                        { opacity: serviceData.id ? 0.5 : 1 }
                                    ]}
                                    placeHolder={stringText.Amount}
                                    keyboardType="numeric"
                                    handelOnChange={(e : any) => {
                                        serviceData?.id
                                            ? setServiceData({
                                                ...serviceData,
                                                amount: e
                                            })
                                            : setServiceDataInput({
                                                ...serviceDataInput,
                                                amount: e
                                            });
                                        props.formValidation(SRFieldName.Amount, e)
                                    }}
                                    editable={serviceData.id ? false : true}
                                    inputValue={
                                        serviceData.id
                                            ? serviceData.amount
                                            : serviceDataInput.amount
                                    }
                                />
                                {errors.amount && (
                                    <RNText style={styles.errorText}>
                                        {errors.amount}
                                    </RNText>
                                )}
                            </View>
                        </>
                    ) : null}
                    <View style={{ gap: 4 }}>
                        <RNText style={styles.priorityText}>
                            {stringText.Priority}
                            <RNText style={styles.importantSign}>*</RNText>
                        </RNText>
                        <Dropdown
                            style={[
                                styles.dropDown,
                                { opacity: serviceData.id ? 0.4 : 1 }
                            ]}
                            data={priorityData}
                            placeholderStyle={{ color: color.BLACK }}
                            itemTextStyle={{ color: color.BLACK }}
                            selectedTextStyle={{ color: color.BLACK }}
                            maxHeight={600}
                            labelField="label"
                            valueField="value"
                            disable={serviceData.id ? true : false}
                            placeholder={stringText.Priority}
                            value={priority}
                            onChange={(e) => {

                                setPriority(e);
                                setServiceDataInput({
                                    ...serviceDataInput,
                                    priority: e.value
                                });
                                props.formValidation(SRFieldName.Priority, e);
                            }}
                        />
                        {errors.priority && (
                            <RNText style={styles.errorText}>
                                {errors.priority}
                            </RNText>
                        )}
                    </View>
                    <View style={{ gap: 4 }}>
                        <RNText style={styles.statusText}>
                            {stringText.Status}
                            <RNText style={styles.importantSign}>*</RNText>
                        </RNText>
                        <Dropdown
                            style={[styles.dropDown, { opacity: 0.5 }]}
                            data={statusData}
                            placeholderStyle={{ color: color.BLACK }}
                            itemTextStyle={{ color: color.BLACK }}
                            selectedTextStyle={{ color: color.BLACK }}
                            maxHeight={600}
                            labelField="label"
                            valueField="value"
                            placeholder={stringText.Status}
                            value={currentStatus}
                            disable={true}
                            onChange={(item: any) => {
                                setCurrentStatus(item.value);
                            }}
                        />
                    </View>
                    {(requestType?.value?.id == '34' &&
                        department?.value.id == '4') ||
                        (requestType?.value?.id == '89' &&
                            department?.value.id == '11') ? (
                        <>
                            <RNText style={styles.statusText}>
                                {stringText.UploadFile}
                                <RNText style={styles.importantSign}>*</RNText>
                            </RNText>
                            <View
                                style={{
                                    flexDirection: serviceData.id ? 'row' : 'column',
                                    columnGap: 20,
                                    gap: 10,

                                }}
                            >
                                <RNButton
                                    handleOnPress={() => {
                                        if (Platform.OS == 'ios') {
                                            askForPermission(PERMISSIONS.IOS.MEDIA_LIBRARY)
                                        } else {
                                            askForPermission(PERMISSIONS.ANDROID.READ_MEDIA_IMAGES)
                                        }
                                    }}

                                    style={{
                                        borderWidth: 1,
                                        borderRadius: 20,
                                        width: '40%',
                                        padding: 10,
                                        alignItems: 'center',
                                        alignContent: 'center',
                                        backgroundColor: color.ACCENT_BLUE,
                                        opacity: serviceData.id ? 0.5 : 1
                                    }}
                                    disabled={serviceData.id ? true : false}
                                >
                                    <RNText style={{ color: color.WHITE }}>
                                        {stringText.UploadFile}
                                    </RNText>
                                </RNButton>
                                {serviceData.id ? (
                                    <RNText style={{ paddingTop: serviceData.id ? 20 : 0 }}>
                                        {
                                            serviceData.id ? '---' :
                                                serviceData.file_paths
                                        }
                                    </RNText>
                                ) : (
                                    <RNText style={{ paddingLeft: 10 }}>
                                        {selectedFile != null &&
                                            selectedFile.name
                                            ? selectedFile.name
                                            : 'No File Selected'}
                                    </RNText>
                                )}
                                {errors.file && (
                                    <RNText style={styles.errorText}>
                                        {errors.file}
                                    </RNText>
                                )}
                            </View>
                        </>
                    ) : null}
                </View>
                {!serviceData.id && (
                    <View style={styles.buttonContainer}>
                        <RNButton
                            handleOnPress={() => {
                                if (!props.isFormValid(serviceDataInput)) {
                                    return;
                                }
                                if (checked == 'drs') {
                                    if (
                                        employeeError == false &&
                                        drsEmpSelected.value != ''
                                    ) {
                                        handelSave(serviceDataInput);
                                        successToast(
                                            'Successfully created the Service Request.');

                                    } else {
                                        SetEmployeeError(true);
                                    }
                                } else {
                                    handelSave(serviceDataInput);
                                    successToast(
                                        'Successfully created the Service Request.');

                                }
                            }}
                            style={styles.createButton}
                        >
                            <RNText style={styles.createText}>
                                {stringText.Save}
                            </RNText>
                        </RNButton>
                    </View>
                )}

                {serviceData.srId && (
                    <View style={styles.buttonContainer}>
                        <RNButton
                            handleOnPress={() => {
                                setComment('');
                                setIsAddCommentOpen(true)
                            }
                            }
                            style={styles.addCommentButton}
                        >
                            <RNText style={styles.addCommentText}>
                                {stringText.AddComment}
                            </RNText>
                        </RNButton>
                    </View>
                )}
                {serviceData.comments.length > 0 && (
                    <View style={styles.commentsWrapper}>
                        <RNText style={styles.commentsTitle}>Comments</RNText>
                        {serviceData.comments
                            .sort(
                                (a, b) =>
                                    new Date(b.created_at).getTime() -
                                    new Date(a.created_at).getTime()
                            )
                            .map((comment) => (
                                <ServiceRequetsCommentsCard
                                    key={comment.id}
                                    commentData={comment}
                                    timeAgo={timeAgo}
                                />
                            ))}
                    </View>
                )}
                {
                    serviceData.srId ? <View /> : <View style={{ height: 100 }} />
                }
            </ScrollView>
            <AddCommentModel
                navigation={navigation}
                setActionPopupVisibility={setIsAddCommentOpen}
                visible={isAddCommentOpen}
                comment={comment}
                setComment={setComment}
                handelPostComment={handelPostComment}
            />
            <ErrorHandlerPopup
                errorHandlerClicked={errorHandlerClicked}
                errorHandlerMessage={errorHandlerMessage}
                visible={errorHandlerVisibility}
            />
            <Spinner animating={fetching} />
        </SafeAreaView >
    );
};

export default EditServiceRequestsView;

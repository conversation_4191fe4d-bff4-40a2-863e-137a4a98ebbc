import React, { FC } from 'react';
import { Modal, View } from 'react-native';
import { Card } from '@rneui/themed';
import { stringText } from '../../utils/constants/stringsText';
import styles from './style';
import RNText from '../../component/RNText';
import { color } from '../../utils/constants/color';
import RNTextInput from '../../component/RNTextInput';
import { ServiceDataTypes } from '.';
import RNButton from '../../component/RNButton';

export type Props = {
    visible?: boolean;
    navigation: any;
    setActionPopupVisibility: (visible: boolean) => void;
    comment: string;
    setComment: (value: string) => void;
    handelPostComment: () => void;
};

const AddCommentModel: FC<Props> = (props) => {
    const {
        visible,
        navigation,
        setActionPopupVisibility,
        comment,
        setComment,
        handelPostComment
    } = props;

    return (
        <Modal transparent={visible} visible={visible} animationType="fade">
            <View style={styles.backdropLight}>
                <Card
                    wrapperStyle={{ alignItems: 'center' }}
                    containerStyle={[
                        styles.modelView,
                        {
                            elevation: 4,
                            borderRadius: 6,
                            margin: 0
                        }
                    ]}
                >
                    <RNText style={styles.commentTitle}>
                        {stringText.AddComment}
                    </RNText>
                    <RNTextInput
                        isMultiline={true}
                        style={{
                            width: '100%',
                            borderRadius: 10,
                            minHeight: 40,
                            maxHeight: 200,
                            paddingTop: 10,
                            borderWidth:1
                        }}
                        handelOnChange={setComment}
                        inputValue={comment}
                        placeHolder="Enter comment"
                    />
                    <View style={styles.modelButtonView}>
                        <RNButton
                            handleOnPress={() => {
                                setComment('');
                                setActionPopupVisibility(!visible);
                            }}
                            style={styles.cancelBtn}
                        >
                            <RNText style={styles.addCommentText}>
                                {stringText.Cancel}
                            </RNText>
                        </RNButton>
                        <RNButton
                            handleOnPress={() => {
                                if (!comment.startsWith(' ')) {
                                    comment.trim().length &&
                                        handelPostComment();
                                }
                            }}
                            style={[
                                styles.createButton,
                                {
                                    opacity: comment.startsWith(' ')
                                        ? 0.4
                                        : comment.trim().length > 0
                                        ? 1
                                        : 0.4
                                }
                            ]}
                        >
                            <RNText style={styles.createText}>
                                {stringText.Post}
                            </RNText>
                        </RNButton>
                    </View>
                </Card>
            </View>
        </Modal>
    );
};

export default AddCommentModel;

import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

const styles = StyleSheet.create({
    scrollContainer: {
        backgroundColor: color.WHITE,
        flexGrow: 1,
        padding: 16
    },
    parentContainer: {
        justifyContent: 'space-between',
        marginBottom: 20,
        gap: 10
    },
    homeWrapper: {},
    importantSign: {
        color: 'red',
        letterSpacing: 8
    },
    departmentTxt: {
        fontSize: 18,
        fontWeight: 'bold',
        color: color.BLACK
    },
    dropDown: {
        marginTop: 5,
        borderWidth: 1,
        borderColor: color.BLACK + 40,
        justifyContent: 'center',
        paddingLeft: 10,
        paddingRight: 10,
        borderRadius: 5,
        height: 40
    },
    requestTypeText: {
        marginTop: 5,
        fontSize: 18,
        fontWeight: 'bold',
        color: color.BLACK
    },
    titleText: {
        marginTop: 5,
        fontSize: 18,
        fontWeight: 'bold',
        color: color.BLACK
    },
    titleTextInput: {
        marginTop: 5,
        borderWidth: 1,
        borderColor: color.BLACK + 40,
        borderRadius: 5,
        color: color.BLACK,
        paddingLeft: 10,
        paddingRight: 10,
        minHeight: 40,
        maxHeight: 2000
    },
    descriptionText: {
        marginTop: 5,
        fontSize: 18,
        fontWeight: 'bold',
        color: color.BLACK
    },
    descriptionTextInput: {
        marginTop: 5,
        borderWidth: 1,
        borderColor: color.BLACK + 40,
        borderRadius: 5,
        color: color.BLACK,
        paddingLeft: 10,
        paddingRight: 10,
        minHeight: 40,
        maxHeight: 2000
    },
    projectTextInput: {
        marginTop: 5,
        borderWidth: 1,
        borderColor: color.BLACK + 40,
        borderRadius: 5,
        color: color.BLACK,
        paddingLeft: 10,
        paddingRight: 10,
        minHeight: 40,
        maxHeight: 500
    },
    priorityText: {
        marginTop: 5,
        fontSize: 18,
        fontWeight: 'bold',
        color: color.BLACK
    },
    statusText: {
        marginTop: 5,
        fontSize: 18,
        fontWeight: 'bold',
        color: color.BLACK
    },
    buttonContainer: {
        flexDirection: 'row',
        justifyContent: 'center',
        alignItems: 'center'
    },
    createButton: {
        marginTop: 10,
        borderWidth: 1,
        backgroundColor: color.DARK_BLUE,
        width: "45%",
        // height: 40,
        paddingVertical:2,
        borderRadius: 10,
        borderColor: color.DARK_BLUE
    },
    createText: {
        textAlign: 'center',
        fontSize: 15,
        padding: 8,
        color: color.WHITE
    },
    addCommentButton: {
        marginTop: 10,
        borderWidth: 2,
        backgroundColor: color.WHITE,
        paddingHorizontal:20,
        paddingVertical:2,
        // height: 40,
        borderRadius: 10,
        borderColor: color.DARK_BLUE
    },
    cancelBtn:{
        marginTop: 10,
        borderWidth: 2,
        backgroundColor: color.WHITE,
        width: "45%",
        paddingVertical:2,
        // height: 40,
        borderRadius: 10,
        borderColor: color.DARK_BLUE
    },
    addCommentText: {
        textAlign: 'center',
        fontSize: 15,
        padding: 8,
        color: color.DARK_BLUE
    },
    radioBtn: {
        padding: 2,
        marginTop: 20
    },
    ButtonImg: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        width:"95%"
    },
    calenderView: {
        alignSelf: 'center',
        justifyContent: 'center',
        padding: 10,
        height: 18,
        width: "15%",
        resizeMode: 'contain'
    },
    datePickerLableWrapper: {
        alignItems: 'flex-start',
        width: '49%'
    },
    datePickerWrapper: {
        flexDirection: 'row',
        alignItems: 'center',
        // justifyContent: 'center',
        gap: 10,
        flex: 1,
        marginVertical: 10,
        width:"80%"
    },
    datePickerLable: {
        fontSize: 18,
        fontWeight: 'bold',
        color: color.BLACK
    },
    inputText: {
        borderWidth: 1,
        borderColor: color.DARK_BLUE,
        width: "100%",
        paddingTop:10,
        height: 39,
        borderRadius: 5,
        backgroundColor: color.WHITE,
        fontSize: 12,
        color: color.GREY_COLOR,
        fontFamily: 'Poppins-Regular',
        paddingLeft:5,
    },
    dateCard: {
        padding: 8,
        flex: 1,
        // borderWidth: 1,
        borderColor: color.BLACK + 40,
        alignItems: 'center',
        borderRadius: 6,
        backgroundColor: color.DARK_BLUE + 40
    },

    // Service Requests comments Card style

    commentsWrapper: {
        marginBottom: 40
    },

    commentsTitle: {
        textAlign: 'center',
        color: color.BLACK,
        marginVertical: 20,
        fontSize: 22
    },

    serviceRequestCommentCard: {
        gap: 16,
        marginVertical: 10,
        backgroundColor: color.WHITE,
        padding: 16,
        elevation: 6,
        shadowColor: color.BLACK + 40,
        borderRadius: 8,
        borderWidth: 2,
        borderColor: color.BLACK + 10
    },
    commentDayAndCommenterName: {
        fontSize: 16,
        color: color.DARK_RED
    },
    commentText: {
        width: '100%',
        borderWidth: 1,
        borderTopRightRadius: 10,
        borderBottomLeftRadius: 10,
        borderBottomRightRadius: 10,
        borderColor: color.BLACK + 20,
        fontSize: 16,
        color: color.BLACK,
        padding: 10,
        elevation: 1,
        backgroundColor: color.WHITE
    },
    projectText: {
        marginTop: 5,
        fontSize: 18,
        fontWeight: 'bold',
        color: color.BLACK
    },

    // comment text inputmodel
    backdropLight: {
        width: '100%',
        height: '100%',
        alignItems: 'center',
        justifyContent: 'center',
        backgroundColor: color.BLACK + 90
    },
    modelView: {
        width: '90%',
        padding: 16,
        gap: 20
    },
    commentTitle: {
        marginVertical: 20,
        fontSize: 18,
        fontWeight: '600',
        color: color.BLACK
    },
    modelButtonView: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 20,
        marginVertical: 20
    },
    errorText: {
        fontSize: 14,
        color: color.DARK_RED,
        marginVertical: 4
    },
    uploadBtn: {
        borderWidth: 1,
        borderRadius: 5,
        width: '40%',
        padding: 10,
        alignItems: 'center',
        alignContent: 'center',
        backgroundColor: color.ACCENT_BLUE
    }
});

export default styles;

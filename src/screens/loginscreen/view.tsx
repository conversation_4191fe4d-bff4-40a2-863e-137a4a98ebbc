import React from 'react';
import {
    SafeAreaView,
    View,
    Linking
} from 'react-native';
import styles from './styles';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Spinner from '../../component/Loader';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import { color } from '../../utils/constants/color';
import { stringText } from '../../utils/constants/stringsText';
import RNText from '../../component/RNText';
import RNImage from '../../component/RNImage';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import RNButton from '../../component/RNButton';
import imageConstant from '../../utils/constants/imageConstant';

export type Props = {
    passwordErrorMessage: String;
    loader: Boolean;
    emailError: String;
    getUserName: (text: string) => void;
    getPassWord: (text: string) => void;
    loggedIn: () => any;
    errorHandlerVisibility: any;
    errorHandlerMessage: any;
    errorHandlerClicked: (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => void;
    showPassword: any;
    toggleShowPassword: any;
    onGoogleButtonPress:()=>void;
};

const LoginView = (props: Props) => {
    const {
        passwordErrorMessage,
        loader,
        emailError,
        getUserName,
        getPassWord,
        loggedIn,
        errorHandlerVisibility,
        errorHandlerMessage,
        errorHandlerClicked,
        showPassword,
        toggleShowPassword,onGoogleButtonPress
    } = props;

    const CopyRightString = stringText.CopyRightString;
    const CopyRightStringArray = CopyRightString.split(' ');

    

    return (
        <>
            <KeyboardAwareScrollView style={styles.mainView}>
                <SafeAreaView>
                    <RNImage
                        source={imageConstant.KuberFinalLogo}
                        resizeMode="contain"
                        style={styles.imageView}
                    />
                    <View style={styles.otherLogo}>
                        <View style={styles.logInWithGoogleButton}>
                            <RNButton
                                style={styles.Button}
                                handleOnPress={() => onGoogleButtonPress()}
                            >
                                <RNText style={styles.textButton}>
                                    {stringText.LoginInWithGoogleText}
                                </RNText>
                            </RNButton>
                        </View>
                        <RNText
                            style={[
                                styles.copyRightText,
                                { textAlign: 'center', marginTop: 10 }
                            ]}
                        >
                            {CopyRightStringArray?.map((text: string) => {
                                if (text === 'XXXXX.') {
                                    return (
                                        <RNText
                                            onPress={() => {
                                                Linking.openURL(
                                                    'https://tudip.com/'
                                                );
                                            }}
                                            style={[
                                                styles.copyRightText,
                                                { color: '#C11728' }
                                            ]}
                                        >
                                            {stringText.TudipTechnologies}{` `}
                                        </RNText>
                                    );
                                }
                                if (text === 'YYYYY.') {
                                    return (
                                        <RNText
                                            onPress={() => {
                                                Linking.openURL(
                                                    'https://tudip.com/privacy-policy/'
                                                );
                                            }}
                                            style={[
                                                styles.copyRightText,
                                                { color: '#C11728' }
                                            ]}
                                        >
                                            {stringText.PrivacyPolicy}{` `}
                                        </RNText>
                                    );
                                }
                                return (
                                    <RNText style={styles.copyRightText}>
                                        {text}{` `}
                                    </RNText>
                                );
                            })}
                        </RNText>
                    </View>
                </SafeAreaView>
            </KeyboardAwareScrollView>
            <Spinner animating={loader} />
            <ErrorHandlerPopup
                visible={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
        </>
    );
};

export default LoginView;

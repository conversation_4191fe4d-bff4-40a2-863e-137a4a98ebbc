import AsyncStorage from '@react-native-async-storage/async-storage';
import React, { useEffect, useState } from 'react';
import { StackActions } from '@react-navigation/native';
import { httpPost } from '../../utils/http';
import validate from '../../utils/validation';
import LoginView from './view';
import { stringText } from '../../utils/constants/stringsText';
import { GoogleSignin } from '@react-native-google-signin/google-signin';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import { Platform } from 'react-native';
import Toast from 'react-native-root-toast';
import { successToast } from '../../component/SuccessToast';

const Login = (props: any) => {
    const { navigation } = props;

    const [userName, setUserName] = useState<string>('');
    const [password, setPassword] = useState<string>('');
    const [passwordErrorMessage, setPasswordErrorMessage] =
        useState<string>('');
    const [loader, setLoader] = useState<boolean>(false);
    const [emailError, setEmailError] = useState<string>('');
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [showPassword, setShowPassword] = useState<boolean>(false);

    const stayLogin = async () => {
        const usertoken = await AsyncStorage.getItem('AuthToken');
        if (usertoken !== null) {
            setLoader(false);
            navigation.dispatch(
                StackActions.replace(navigationStringText.NavigationDrawer)
            );
        }
    };

    const handleGoogleSignIn = async () => {
        try {
            setLoader(true);
            await GoogleSignin.signOut();
            const userInfo = await GoogleSignin.signIn();
            const googleCredential = await GoogleSignin.getTokens();
            if (userInfo && googleCredential) {
                const tudipEmailRegex = /@tudip\.com$/;
                const email = userInfo.user.email ?? '';

                if (tudipEmailRegex.test(email)) {
                    UserTokenAPI(googleCredential.accessToken);
                    setLoader(false);
                } else {
                    GoogleSignin.revokeAccess();
                    GoogleSignin.signOut();
                    errorHandlerClicked(true, 'Please use Tudip email only');
                    setLoader(false);
                }
            }
        } catch (error: any) {
            setLoader(false);
            console.error('Google Sign-In Error:', error);
            successToast(error.toString());
        }
    };

    const UserTokenAPI = (token: any) => {
        httpPost(`/google-authentication`, { token })
            .then(async (response: any) => {
                const responseData = JSON.parse(response).data;
                setLoader(false);
                successToast('User Successfully Authenticated');
                AsyncStorage.setItem(
                    'AuthToken',
                    responseData.access_token
                ).finally(
                    navigation.dispatch(
                        StackActions.replace(
                            navigationStringText.NavigationDrawer
                        )
                    )
                );
            })
            .catch((err: any) => {
                setLoader(false);
                GoogleSignin.revokeAccess();
                GoogleSignin.signOut();
                // successToast(err.toString());
                errorHandlerClicked(true, `${err?.response?.data?.message}`);
            });
    };

    useEffect(() => {
        GoogleSignin.configure();
        stayLogin();
    }, []);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    const loggedIn = () => {
        const userNameErrorMessage = validate('Email', userName);
        if (userNameErrorMessage) {
            setEmailError(userNameErrorMessage);
        } else if (userName.length < 1) {
            setEmailError(`${stringText.EmailErrorMessage}`);
        } else if (password.length < 1) {
            setPasswordErrorMessage(`${stringText.PasswordErrorMessage}`);
        } else {
            setLoader(true);
            const payload = {
                username: userName,
                password: password
            };
            httpPost('/api/login', payload)
                .then(async (response: any) => {
                    const AuthToken = await JSON.parse(response).data.token;

                    if (AuthToken) {
                        AsyncStorage.setItem('AuthToken', AuthToken)
                            .then(() => {
                                setLoader(false);
                                navigation.dispatch(
                                    StackActions.replace('NavigationDrawer')
                                );
                            })
                            .catch((err) => {
                                setLoader(false);
                            });
                    }
                })
                .catch((err: any) => {
                    setLoader(false);
                    if (
                        err?.response?.data?.message !== undefined &&
                        err?.response?.data?.message !== null &&
                        err?.response?.data?.message !== ''
                    ) {
                        console.log(err.response.data.message);
                        errorHandlerClicked(true, err?.response?.data?.message);
                    } else {
                        errorHandlerClicked(
                            true,
                            `${stringText.SomethingWentwrong}`
                        );
                    }
                });

        }
    };

    const getUserName = (text: string) => {
        setEmailError('');
        setPasswordErrorMessage('');
        setUserName(text);
    };

    const getPassWord = (passWord: string) => {
        setEmailError('');
        setPasswordErrorMessage('');
        setPassword(passWord);
    };

    const toggleShowPassword = () => {
        setShowPassword(!showPassword);
    };

    return (
        <LoginView
            passwordErrorMessage={passwordErrorMessage}
            loader={loader}
            emailError={emailError}
            errorHandlerVisibility={errorHandlerVisibility}
            errorHandlerMessage={errorHandlerMessage}
            errorHandlerClicked={errorHandlerClicked}
            getUserName={getUserName}
            getPassWord={getPassWord}
            loggedIn={loggedIn}
            showPassword={showPassword}
            toggleShowPassword={toggleShowPassword}
            onGoogleButtonPress={handleGoogleSignIn}
        />
    );
};

export default Login;

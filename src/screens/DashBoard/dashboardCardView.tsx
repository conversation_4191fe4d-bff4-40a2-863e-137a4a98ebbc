import React, { useState } from 'react';
import { FlatList, RefreshControl, SafeAreaView, ScrollView, TouchableOpacity, View } from 'react-native';
import Spinner from '../../component/Loader';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import styles from './style';
import { stringText } from '../../utils/constants/stringsText';
import RNText from '../../component/RNText';
import imageConstant from '../../utils/constants/imageConstant';
import SearchBar from '../../component/SearchBar';
import { color } from '../../utils/constants/color';
import RNImage from '../../component/RNImage';
import RNButton from '../../component/RNButton';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import FontAwesome5 from 'react-native-vector-icons/FontAwesome5';
import MaterialIcons from 'react-native-vector-icons/MaterialIcons';

export type Props = {
    navigation: any;
    refreshing: boolean;
    otherData: any;
    fetching: boolean;
    errorHandlerVisibility: boolean;
    errorHandlerMessage: string;
    errorHandlerClicked: (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => void;
    onRefresh: () => void,
    employeeList: any;
};

const DashBoardCardView = (props: Props) => {
    let ListviewRef;

    const {
        navigation,
        refreshing,
        fetching,
        errorHandlerVisibility,
        errorHandlerMessage,
        errorHandlerClicked,
        onRefresh,
        otherData,
        employeeList
    } = props;

    const [queryStr, setQueryStr] = useState<string>('');
    const filteredEmployees = employeeList.filter((employee: any) =>
        employee.name.toLowerCase().includes(queryStr.toLowerCase().trim())
    );
    const formatDate = (isoDate: string) => {
        const date = new Date(isoDate);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-based
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    return (
        <>
            <SafeAreaView>
                {/* <ScrollView
                    refreshControl={
                        <RefreshControl
                            refreshing={refreshing}
                            onRefresh={onRefresh}
                        />
                    }
                > */}
                <View style={styles.parentContainer}>
                    <SearchBar
                        handelOnChange={(searchStr: string) => {
                            setQueryStr(searchStr);
                        }}
                        value={queryStr}
                        placeHolder="Search Employee"
                    />
                    {queryStr.trim().length > 0 ? (
                        <View style={styles.employeeListView}>
                            <FlatList
                                data={filteredEmployees}
                                persistentScrollbar={true}
                                showsVerticalScrollIndicator={true}
                                keyExtractor={(item) => item.userId.toString()}
                                renderItem={({ item }) => (
                                    <View style={styles.employeeCard}>
                                        <RNButton
                                            ActiveOpacity={0.8}
                                            handleOnPress={() => {
                                                setQueryStr("");
                                                navigation.push(navigationStringText.EmployeeDetails, { empId: item.userId });
                                            }}
                                        >
                                            <RNText style={styles.text}>{item.name}</RNText>
                                        </RNButton>
                                    </View>
                                )}
                            />
                        </View>
                    ) : null}
                    {
                        !fetching &&
                        <ScrollView
                            refreshControl={
                                <RefreshControl
                                    refreshing={refreshing}
                                    onRefresh={()=>{ 
                                        onRefresh();
                                        setQueryStr('');
                                    }}
                                />
                            }
                            showsVerticalScrollIndicator={false}
                        >
                            <View style={styles.cardView}>
                                <View style={styles.cardHeadingView}>
                                    <View style={styles.rowDirection}>
                                        <MaterialIcons
                                            name="highlight"
                                            color={color.DARK_BLUE}
                                            size={24}
                                        />
                                        <RNText style={styles.cardHeading}>{stringText.TodayHighlights}</RNText>
                                    </View>
                                    <RNButton ActiveOpacity={0.8} handleOnPress={() => { navigation.navigate(navigationStringText.HIGHLIGHT) }}>
                                        <RNImage source={imageConstant.OpenCard} style={{ height: 24, width: 24 }} resizeMode={'contain'} />
                                    </RNButton>
                                </View>
                                <View style={styles.valueView}>
                                    <RNText style={styles.text}>{`${stringText.BirthdayCount}: ${otherData?.todaysHighlights?.todaysBirthDayCount}`} </RNText>
                                    <RNText style={styles.text}>{`${stringText.LunchTittle}: ${otherData?.todaysHighlights?.lunch}`} </RNText>
                                    <RNText style={styles.text}>{`${stringText.DinnerTittle}: ${otherData?.todaysHighlights?.dinner}`} </RNText>
                                </View>
                            </View>

                            <View style={styles.cardView}>
                                <View style={styles.cardHeadingViewProject}>
                                    <FontAwesome5
                                        name="project-diagram"
                                        color={color.DARK_BLUE}
                                        size={20}
                                    />
                                    <RNText style={styles.cardHeading}>{stringText.Project}</RNText>
                                </View>
                                <View style={styles.valueView}>
                                    <FlatList
                                        data={otherData?.projectDetails?.projectInfo}
                                        renderItem={({ item }) => (
                                            <RNText style={styles.text}> {`${item}`} </RNText>
                                        )}
                                    />
                                    <RNText style={styles.text}>{`${stringText.Status}: ${otherData?.projectDetails?.resourseStatus}`} </RNText>
                                </View>
                            </View>

                            {otherData?.DRS?.count > 0 &&
                                <View style={styles.cardView}>
                                    <View style={styles.cardHeadingView}>
                                        <View style={styles.rowDirection}>
                                            <MaterialCommunityIcons
                                                name="contacts"
                                                color={color.DARK_BLUE}
                                                size={24}
                                            />
                                            <RNText style={styles.cardHeading}>{stringText.DRs}</RNText>
                                        </View>
                                        <RNButton ActiveOpacity={0.8} handleOnPress={() => { navigation.navigate(navigationStringText.EmployeeTreeDetails) }}>
                                            <RNImage source={imageConstant.OpenCard} style={{ height: 24, width: 24 }} resizeMode={'contain'} />
                                        </RNButton>
                                    </View>
                                    <View style={styles.valueView}>
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '70%' }}>
                                            <RNText style={[styles.text, styles.redText]}>{`${stringText.Free}: ${otherData?.DRS?.free}`}</RNText>
                                            <RNText style={[styles.text, styles.redText]}>{`${stringText.Additional}: ${otherData?.DRS?.additional}`}</RNText>
                                        </View>
                                        <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '70%' }}>
                                            <RNText style={styles.text}>{`${stringText.Billable}: ${otherData?.DRS?.billable}`}</RNText>
                                            <RNText style={styles.text}>{`${stringText.Unbillable}: ${otherData?.DRS?.nonBillable}`}</RNText>
                                        </View>
                                        <RNText style={styles.text}>{`Total: ${otherData?.DRS?.count}`}</RNText>
                                        <RNText style={styles.touchableText} onPress={() => { navigation.navigate(navigationStringText.DRsOnLeave) }} >{stringText.DRsLeaveDetails}</RNText>
                                    </View>
                                </View>
                            }

                            <View style={styles.cardView}>
                                <View style={styles.cardHeadingView}>
                                    <View style={styles.rowDirection}>
                                        <MaterialIcons
                                            name="miscellaneous-services"
                                            color={color.DARK_BLUE}
                                            size={26}
                                        />
                                        <RNText style={styles.cardHeading}>{stringText.ServiceRequests}</RNText>
                                    </View>
                                    <RNButton ActiveOpacity={0.8} handleOnPress={() => { navigation.navigate(navigationStringText.ServiceRequests) }}>
                                        <RNImage source={imageConstant.OpenCard} style={{ height: 24, width: 24 }} resizeMode={'contain'} />
                                    </RNButton>
                                </View>
                                <View style={styles.valueView}>
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '70%' }}>
                                        <RNText style={styles.text}>{`${stringText.Open}: ${otherData?.serviceRequest?.pendingRequest}`}</RNText>
                                        <RNText style={styles.text}>{`${stringText.Closed}: ${otherData?.serviceRequest?.closedRequest}`}</RNText>
                                    </View>
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '70%' }}>
                                        <RNText style={styles.text}>{`${stringText.InProgress}: ${otherData?.serviceRequest?.inProgressRequest}`}</RNText>
                                        <RNText style={styles.text}>{`${stringText.Reopen}: ${otherData?.serviceRequest?.reOpenRequest}`}</RNText>
                                    </View>
                                    <View style={{ flexDirection: 'row', justifyContent: 'space-between', width: '70%' }}>
                                        <RNText style={styles.text}>{`${stringText.notFixed}: ${otherData?.serviceRequest?.wontFixedRequest}`}</RNText>
                                        <RNText style={styles.text}>{`${stringText.Total}: ${otherData?.serviceRequest?.totalRequest}`}</RNText>
                                    </View>
                                </View>
                            </View>

                            <View style={styles.cardView}>
                                <View style={styles.cardHeadingView}>
                                    <View style={styles.rowDirection}>
                                        <MaterialIcons
                                            name="important-devices"
                                            color={color.DARK_BLUE}
                                            size={22}
                                        />
                                        <RNText style={styles.cardHeading}>{stringText.Assets}</RNText>
                                    </View>
                                    <RNButton ActiveOpacity={0.8} handleOnPress={() => { navigation.navigate(navigationStringText.AssetDetails) }}>
                                        <RNImage source={imageConstant.OpenCard} style={{ height: 24, width: 24 }} resizeMode={'contain'} />
                                    </RNButton>
                                </View>
                                <View style={styles.valueView}>
                                    <RNText style={styles.text}>{`${stringText.AssetId}: ${otherData?.laptopDetails?.laptopId}`}</RNText>
                                    <RNText style={styles.text}>{`${stringText.Brand}: ${otherData?.laptopDetails?.Brand}`}</RNText>
                                    <RNText style={styles.text}>{`${stringText.AssignedDate}: ${formatDate(otherData?.laptopDetails?.AssignedOn)}`}</RNText>
                                </View>
                            </View>

                            <View style={styles.cardView}>
                                <View style={styles.cardHeadingView}>
                                    <View style={styles.rowDirection}>
                                        <MaterialIcons
                                            name="details"
                                            color={color.DARK_BLUE}
                                            size={26}
                                        />
                                        <RNText style={styles.cardHeading}>{stringText.LeaveDetails}</RNText>
                                    </View>
                                    <RNButton ActiveOpacity={0.8} handleOnPress={() => { navigation.navigate(navigationStringText.LeavesDetails) }}>
                                        <RNImage source={imageConstant.OpenCard} style={{ height: 24, width: 24 }} resizeMode={'contain'} />
                                    </RNButton>
                                </View>
                                <View style={styles.valueView}>
                                    <RNText style={styles.text}>{`${stringText.TotalLeavesAllocated}: ${otherData?.leaveDetails?.totalLeaveAllocated}`}</RNText>
                                    <RNText style={styles.text}>{`${stringText.LeavesAvailed}: ${otherData?.leaveDetails?.leavesAvailed}`}</RNText>
                                    <RNText style={styles.text}>{`${stringText.LeaveBalance}: ${otherData?.leaveDetails?.leaveBalance}`}</RNText>
                                </View>
                            </View>

                            <View style={styles.cardView}>
                                <View style={styles.cardHeadingView}>

                                    <View style={styles.rowDirection}>
                                        <RNImage source={imageConstant.Color_Holiday} style={{ height: 24, width: 24 }} resizeMode={'contain'} />
                                        <RNText style={styles.cardHeading}>{stringText.Holiday}</RNText>
                                    </View>
                                    <RNButton ActiveOpacity={0.8} handleOnPress={() => { navigation.navigate(navigationStringText.HolidayList) }}>
                                        <RNImage source={imageConstant.OpenCard} style={{ height: 24, width: 24 }} resizeMode={'contain'} />
                                    </RNButton>
                                </View>
                                <View style={styles.valueView}>
                                    <RNText style={styles.text}>{`${stringText.UpcomingHolidays}: ${otherData?.holidayList?.upcomingHoliday}`}</RNText>
                                    <RNText style={styles.text}>{`${stringText.TotalHolidays}: ${otherData?.holidayList?.totalHolidays}`}</RNText>
                                </View>
                            </View>

                            <View style={styles.cardView}>
                                <View style={styles.cardHeadingView}>
                                    <View style={styles.rowDirection}>
                                        <MaterialCommunityIcons
                                            name="lightbulb"
                                            color={color.DARK_BLUE}
                                            size={24}
                                        />
                                        <RNText style={styles.cardHeading}>{stringText.YourInfo}</RNText>
                                    </View>
                                    <RNButton ActiveOpacity={0.8} handleOnPress={() => { navigation.navigate(navigationStringText.YourInfo) }}>
                                        <RNImage source={imageConstant.OpenCard} style={{ height: 24, width: 24 }} resizeMode={'contain'} />
                                    </RNButton>
                                </View>
                                <View style={styles.valueView}>
                                    <RNText style={styles.text}>{`${stringText.Name}: ${otherData?.basicInfo?.name}`}</RNText>
                                    <RNText style={styles.text}>{`${stringText.Designation}: ${otherData?.basicInfo?.designation}`}</RNText>
                                    <RNText style={styles.text}>{`${stringText.IdText}: ${otherData?.basicInfo?.employeeId}`}</RNText>
                                    <RNText style={styles.text}>{`${stringText.Manager}: ${otherData?.basicInfo?.managerName}`}</RNText>
                                </View>
                            </View>
                        </ScrollView>
                    }
                </View>
                {/* </ScrollView> */}
            </SafeAreaView>
            <Spinner animating={fetching} />
            <ErrorHandlerPopup
                visible={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
        </>
    );
};

export default DashBoardCardView;

import React, { useEffect, useState } from 'react';
import { SafeAreaView, ScrollView, View } from 'react-native';
import RNText from '../../component/RNText';
import styles from '../EmployeeInfoScreen/styles';
import { stringText } from '../../utils/constants/stringsText';
import { RadioButton } from 'react-native-paper';
import { Dropdown } from 'react-native-element-dropdown';
import { color } from '../../utils/constants/color';
import { httpGet } from '../../utils/http';
import RNActivityIndicator from '../../component/Loader';
import apiConstant from '../../utils/constants/apiConstant';

type EmployeeInfoScreen = {
    navigation: any;
};

const getAllTshirtSize = [
    { label: 'Select', value: 0 },
    { label: 'S', value: 1 },
    { label: 'M', value: 2 },
    { label: 'L', value: 3 },
    { label: 'XL', value: 4 },
    { label: 'XXL', value: 5 },
    { label: 'XXXL', value: 6 }
];

const EmployeeInfoViewTab3: React.FC<EmployeeInfoScreen> = () => {
    const [managerDropDown, setManagerDropDown] = useState<[]>([]);
    const [referredBy, setReferredBy] = useState(null);
    const [referredByName, setReferredByName] = useState('');
    const [tShirtSize, setTShirtSize] = useState(null);

    const [firstPerfomance, setFirstPerfomance] = useState('');
    const [idCard, setIdCard] = useState('');
    const [mediclaim, setMediclaim] = useState('');
    const [underPerformance, setUnderPerformance] = useState('');
    const [underMaternity, setUnderMaternity] = useState('');

    const [isFocusTShirtSize, setIsFocusTShirtSize] = useState(false);
    const [isFocusReferredBy, setIsFocusReferredBy] = useState(false);

    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [employeeInfo, setEmployeeInfo] = useState<any>('');

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    const RenderTShirtSize = () => {
        if (tShirtSize != null || isFocusTShirtSize) {
            return (
                <RNText
                    style={[
                        styles.label,
                        isFocusTShirtSize && { color: color.ACCENT_BLUE }
                    ]}
                >
                    T-Shirt Size
                </RNText>
            );
        }
        return null;
    };

    const RenderReferredBy = () => {
        if (referredBy == null || referredBy || isFocusReferredBy) {
            return (
                <RNText
                    style={[
                        styles.label,
                        isFocusReferredBy && { color: color.ACCENT_BLUE }
                    ]}
                >
                    Referred By
                </RNText>
            );
        }
        return null;
    };

    const RenderMediclaimNumber = () => {
        return (
            <RNText
                style={[
                    styles.label,
                    isFocusReferredBy && { color: color.ACCENT_BLUE }
                ]}
            >
                {stringText.MediclaimNumber}
            </RNText>
        );
        return null;
    };

    useEffect(() => {
        setFetching(true);
        httpGet(apiConstant.EMP_INFO)
            .then((response: any) => {
                const employeeInfodata = JSON.parse(response)?.data;
                if (employeeInfodata) {
                    setEmployeeInfo(employeeInfodata);
                    setFirstPerfomance(
                        employeeInfodata.first_performance_review.toString()
                    );
                    setIdCard(employeeInfodata.id_card_issued.toString());
                    setMediclaim(employeeInfodata.mediclaim_issued.toString());
                    setUnderMaternity(
                        employeeInfodata.under_maternity.toString()
                    );
                    setUnderPerformance(
                        employeeInfodata.under_performance_review.toString()
                    );
                    setReferredBy(employeeInfodata.referred_by);
                    setTShirtSize(employeeInfodata.tshirt_size);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }, []);

    useEffect(() => {
        setFetching(true);
        httpGet(apiConstant.GET_ALL_EMP)
            .then((response: any) => {
                const managerData = JSON.parse(response)?.data;
                if (managerData) {
                    setManagerDropDown(managerData);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }, []);

    useEffect(() => {
        httpGet(`${apiConstant.USER_ID}/${referredBy}`)
            .then((response: any) => {
                const referredByName = JSON.parse(response)?.data;

                if (referredByName) {
                    setReferredByName(referredByName.name);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }, [referredBy]);

    return (
        <SafeAreaView>
            <ScrollView>
                <View style={styles.container}>
                    <View style={styles.Info}>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.titleText}>
                                {stringText.FirstPerformanceReview}
                            </RNText>
                            <RadioButton.Group
                                onValueChange={(newValue) =>
                                    setFirstPerfomance(newValue)
                                }
                                value={firstPerfomance}
                            >
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center'
                                    }}
                                >
                                    <RadioButton.Item
                                        mode="android"
                                        label="Yes"
                                        position="leading"
                                        style={{
                                            marginLeft: -15,
                                            marginBottom: -5
                                        }}
                                        value="1"
                                        disabled={true}
                                    />
                                    <RadioButton.Item
                                        mode="android"
                                        label="No"
                                        position="leading"
                                        style={{
                                            marginLeft: -15,
                                            marginBottom: -5
                                        }}
                                        value="0"
                                        disabled={true}
                                    />
                                </View>
                            </RadioButton.Group>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.titleText}>
                                {stringText.IdCardIssued}
                            </RNText>
                            <RadioButton.Group
                                onValueChange={(newValue) =>
                                    setIdCard(newValue)
                                }
                                value={idCard}
                            >
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center'
                                    }}
                                >
                                    <RadioButton.Item
                                        mode="android"
                                        label="Yes"
                                        position="leading"
                                        style={{
                                            marginLeft: -15,
                                            marginBottom: -5
                                        }}
                                        value="1"
                                        disabled={true}
                                    />

                                    <RadioButton.Item
                                        mode="android"
                                        label="No"
                                        position="leading"
                                        style={{
                                            marginLeft: -15,
                                            marginBottom: -5
                                        }}
                                        value="0"
                                        disabled={true}
                                    />
                                </View>
                            </RadioButton.Group>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.titleText}>
                                {stringText.MediclaimIssued}
                            </RNText>
                            <RadioButton.Group
                                onValueChange={(newValue) =>
                                    setMediclaim(newValue)
                                }
                                value={mediclaim}
                            >
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center'
                                    }}
                                >
                                    <RadioButton.Item
                                        mode="android"
                                        label="Yes"
                                        position="leading"
                                        style={{
                                            marginLeft: -15,
                                            marginBottom: -5
                                        }}
                                        value="1"
                                        disabled={true}
                                    />

                                    <RadioButton.Item
                                        mode="android"
                                        label="No"
                                        position="leading"
                                        style={{
                                            marginLeft: -15,
                                            marginBottom: -5
                                        }}
                                        value="0"
                                        disabled={true}
                                    />
                                </View>
                            </RadioButton.Group>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.titleText}>
                                {stringText.underPIP}
                            </RNText>
                            <RadioButton.Group
                                onValueChange={(newValue) =>
                                    setUnderPerformance(newValue)
                                }
                                value={underPerformance}
                            >
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center'
                                    }}
                                >
                                    <RadioButton.Item
                                      mode="android"
                                      label="Yes"
                                      position="leading"
                                      style={{
                                          marginLeft: -15,
                                          marginBottom: -5
                                      }}
                                    value="1" disabled={true} />
                                    
                                    <RadioButton.Item
                                      mode="android"
                                      label="No"
                                      position="leading"
                                      style={{
                                          marginLeft: -15,
                                          marginBottom: -5
                                      }}
                                    value="0" disabled={true} />
                                    
                                </View>
                            </RadioButton.Group>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.titleText}>
                                {stringText.UnderMetrnity}
                            </RNText>
                            <RadioButton.Group
                                onValueChange={(newValue) =>
                                    setUnderMaternity(newValue)
                                }
                                value={underMaternity}
                            >
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center'
                                    }}
                                >
                                    <RadioButton.Item
                                    
                                    mode="android"
                                    label="Yes"
                                    position="leading"
                                    style={{
                                        marginLeft: -15,
                                        marginBottom: -5
                                    }}
                                    value="1" disabled={true} />
                                   
                                    <RadioButton.Item
                                      mode="android"
                                      label="No"
                                      position="leading"
                                      style={{
                                          marginLeft: -15,
                                          marginBottom: -5
                                      }}
                                    value="0" disabled={true} />
                                   
                                </View>
                            </RadioButton.Group>
                        </View>
                        <View style={{ gap: 5, marginBottom: 10 }}>
                            <RNText style={styles.label}>
                                {stringText.MediclaimNumber}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {employeeInfo.mediclaim_no
                                    ? employeeInfo.mediclaim_no
                                    : 'NA'}
                            </RNText>
                        </View>
                        <View
                            style={{ position: 'relative', marginBottom: 10 }}
                        >
                            <RenderTShirtSize />
                            <Dropdown
                                style={[
                                    styles.dropdown,
                                    isFocusTShirtSize && {
                                        borderColor: color.DARK_BLUE
                                    }
                                ]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                iconStyle={styles.iconStyle}
                                data={getAllTshirtSize}
                                disable={true}
                                maxHeight={300}
                                labelField="label"
                                valueField="value"
                                placeholder={
                                    !isFocusTShirtSize ? 'T-Shirt Size' : '...'
                                }
                                value={tShirtSize}
                                onFocus={() => setIsFocusTShirtSize(true)}
                                onBlur={() => setIsFocusTShirtSize(false)}
                                onChange={(item: any) => {
                                    setTShirtSize(item.value);
                                    setIsFocusTShirtSize(false);
                                }}
                            />
                        </View>
                        {/* <View style={{ position: 'relative' }}>
                            <RenderReferredBy />
                            <Dropdown
                                style={[styles.dropdown, isFocusReferredBy && { borderColor: color.DARK_BLUE }]}
                                placeholderStyle={styles.placeholderStyle}
                                selectedTextStyle={styles.selectedTextStyle}
                                iconStyle={styles.iconStyle}
                                data={managerDropDown?.map((data: any) => ({ label: data.info, value: data.userId }))}
                                disable={true}
                                maxHeight={150}
                                labelField="label"
                                valueField="value"
                                placeholder={referredBy != null ? 'Referred By' : 'NA'}
                                value={{ label: managerDropDown[managerDropDown?.findIndex((set: any) => set.userId === employeeInfo.referred_by)]?.info, value: managerDropDown[managerDropDown?.findIndex((set: any) => set.userId === employeeInfo.referred_by)]?.userId }}
                                onFocus={() => setIsFocusReferredBy(true)}
                                onBlur={() => setIsFocusReferredBy(false)}
                                onChange={item => {
                                    setReferredBy(item.value);
                                    setIsFocusReferredBy(false);
                                }}
                            />
                        </View> */}

                        <View style={{ gap: 5, marginBottom: 10 }}>
                            <RNText style={styles.label}>
                                {stringText.ReferredBy}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {referredByName ? referredByName : 'NA'}
                            </RNText>
                        </View>
                    </View>
                </View>
            </ScrollView>
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    );
};
export default EmployeeInfoViewTab3;

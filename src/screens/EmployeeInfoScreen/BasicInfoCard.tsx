import React, { useEffect, useState } from 'react';
import { SafeAreaView, ScrollView, View } from 'react-native';
import RNText from '../../component/RNText';
import styles from './styles';
import { stringText } from '../../utils/constants/stringsText';
import { RadioButton } from 'react-native-paper';
import { Dropdown } from 'react-native-element-dropdown';
import { color } from '../../utils/constants/color';
import AntDesign from 'react-native-vector-icons/AntDesign';
import { httpGet } from '../../utils/http';
import RNActivityIndicator from '../../component/Loader';
import apiConstant from '../../utils/constants/apiConstant';

// type EmployeeInfoScreen = {
//     navigation: any
//     designationData: any
//     designationBandData: any
//     managerData: any
//     agreementData: any
//     employeeInfoData: any
// }

const EmployeeInfoView: React.FC = () => {
    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [pfChecked, setPfChecked] = useState<string>('');
    const [employeeInfo, setEmployeeInfo] = useState<any>('');

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        setFetching(true);
        httpGet(apiConstant.EMP_INFO)
            .then((response: any) => {
                const employeeInfodata = JSON.parse(response)?.data;
                if (employeeInfodata) {
                    setEmployeeInfo(employeeInfodata);
                    setPfChecked(employeeInfodata.pf_applicable.toString());
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    }, []);

    return (
        <SafeAreaView>
            <ScrollView>
                <View style={styles.container}>
                    <View style={styles.Info}>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {stringText.AaadharNumber}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {employeeInfo.aadhaar_card_no
                                    ? employeeInfo.aadhaar_card_no
                                    : 'NA'}
                            </RNText>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {stringText.PanNumber}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {employeeInfo.pan_card_no
                                    ? employeeInfo.pan_card_no
                                    : 'NA'}
                            </RNText>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {stringText.PassportNumber}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {employeeInfo.passport_no
                                    ? employeeInfo.passport_no
                                    : 'NA'}
                            </RNText>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.titleText}>
                                {stringText.PfApplicable}
                            </RNText>
                            <RadioButton.Group
                                onValueChange={(newValue) =>
                                    setPfChecked(newValue)
                                }
                                value={pfChecked}
                            >
                                <View
                                    style={{
                                        flexDirection: 'row',
                                        alignItems: 'center'
                                    }}
                                >
                                    <RadioButton.Item
                                        mode="android"
                                        label="Yes"
                                        value="1"
                                        disabled={true}
                                        position="leading"
                                        style={{
                                            marginLeft: -15,
                                            marginBottom: -5
                                        }}
                                    />
                                    <RadioButton.Item
                                        mode="android"
                                        label="No"
                                        position="leading"
                                        style={{
                                            marginLeft: -15,
                                            marginBottom: -5
                                        }}
                                        value="0"
                                        disabled={true}
                                    />
                                </View>
                            </RadioButton.Group>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {stringText.PfNumber}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {employeeInfo.pf_no ? employeeInfo.pf_no : 'NA'}
                            </RNText>
                        </View>
                        <View style={{ gap: 5 }}>
                            <RNText style={styles.label}>
                                {stringText.UanNumber}
                            </RNText>
                            <RNText style={styles.infoField}>
                                {employeeInfo.uan_no
                                    ? employeeInfo.uan_no
                                    : 'NA'}
                            </RNText>
                        </View>
                    </View>
                </View>
            </ScrollView>
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    );
};

export default EmployeeInfoView;

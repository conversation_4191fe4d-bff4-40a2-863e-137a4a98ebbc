import React, { useState, useEffect } from "react";
import { FlatList, SafeAreaView, View } from "react-native";
import AssetDetailsView from "./AssetDetailsView";
import { httpGet } from "../../utils/http";
import { stringText } from "../../utils/constants/stringsText";
import RNActivityIndicator from "../../component/Loader";
import apiConstant from "../../utils/constants/apiConstant";

export type Props = {
    navigation: any;
};

export type AssetDetailsType = {
    laptopName: string;
    laptop_no: string;
    ram_size: string;
    hard_disk_size: string;
    assigned_on: string;
};

const AssetDetailsScreen = (props: Props) => {
    const { navigation } = props;
    const [assetDetails, setAssetDetails] = useState<AssetDetailsType[]>([]);
    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        setFetching(true);
        httpGet(apiConstant.ASSET)
            .then((response: any) => {
                const assetData = JSON.parse(response)?.data.laptopDetails;
                if (assetData) {
                    setAssetDetails(assetData.map((asset: AssetDetailsType) => ({
                        ...asset,
                        assigned_on: assignedOnDate(asset.assigned_on)
                    })));
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(true, `${stringText.SomethingWentwrong}`);
                }
            });
    }, []);

    const assignedOnDate = (dateString: string): string => {
        const date = new Date(dateString);
        const day = date.getDate();
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        return `${day}/${month}/${year}`;
    };

    const renderItem = ({ item }: { item: AssetDetailsType }) => (
        <AssetDetailsView
            laptopName={item.laptopName}
            laptop_no={item.laptop_no}
            ram_size={item.ram_size}
            hard_disk_size={item.hard_disk_size}
            assigned_on={item.assigned_on}
        />
    );

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <FlatList
                contentContainerStyle={{ paddingTop: 10, paddingBottom: 30 }}
                data={assetDetails}
                renderItem={renderItem}
                keyExtractor={(item, index) => index.toString()}
            />
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    );
};

export default AssetDetailsScreen;

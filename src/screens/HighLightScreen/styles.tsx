import { StyleSheet } from "react-native";
import { color } from "../../utils/constants/color";

const styles = StyleSheet.create({
    mainView: {
        flex: 1,
        backgroundColor: color.OFF_WHITE
    },
    ImageBackTop: {
        height: 120,
        width: '100%',
        // borderRadius: 8,
        // marginTop: 12,
        justifyContent: 'center',
        alignItems: 'center',
        // alignSelf: 'center'
    },
    SecondImageBackTop: {
        height: 120,
        width: '100%',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center'
    },
    ImageBackDown: {
        height: 150,
        width: '100%',
        borderRadius: 8,
        marginTop: 12,
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center',
        // margin:10
    },
    textStyle: {
        fontSize: 19,
        color: color.GREY_COLOR,
        textAlign: 'center',
        fontFamily: 'Poppins-SemiBold'
    },
    textStyleBottomTittle: {
        fontSize: 19,
        color: color.WHITE,
        textAlign: 'left',
        fontFamily: 'Poppins-SemiBold'
    },
    holidaysBottomTittle: {
        fontSize: 13,
        color: color.WHITE,
        textAlign: 'left',
        fontFamily: 'Poppins-SemiBold'
    },
    textStyle1: {
        fontFamily: 'Poppins-Regular',
        fontSize: 25,
        color: color.GREY_COLOR,
        textAlign: 'center'
    },
    cardView: {
        alignSelf: 'center',
        backgroundColor: color.WHITE,
        justifyContent: 'center',
        paddingBottom: 11
    },
    cakeText: {
        fontSize: 12,
        color: color.GREY_COLOR,
        fontFamily: 'Poppins-SemiBold',
        marginTop: 2,
        textAlign: 'center',
        width: 100
    },
    cakeNameText: {
        textAlign: 'center',
        fontSize: 10,
        color: color.GREY_COLOR,
        fontFamily: 'Poppins-Regular',
        marginTop: 2,
        width: 100
    },
    cakeImage: {
        alignSelf: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 15
    },
    cardTopRightView: {
        width: 36,
        height: 36,
        backgroundColor: color.LIGHT_BLUE,
        borderTopRightRadius: 8,
        borderBottomRightRadius: 8,
        justifyContent: 'center',
        alignItems: 'center'
    },
    cardTopRightText: {
        fontFamily: 'Poppins-SemiBold',
        fontSize: 10,
        color: color.WHITE
    },
    viewText: {
        marginLeft: 12,
        marginBottom: 41
    },
    styleText: {
        left: 10,
        fontSize: 15,
        color: color.ACCENT_BLUE,
        marginBottom: 18,
        fontFamily: 'Poppins-SemiBold'
    },
    ImgPro: {
        height: 48,
        width: 48,
        borderWidth: 1,
        borderRadius: 100,
        alignSelf: 'center',
        borderColor: color.DARK_RED,
        marginLeft: 25,
        marginRight: 15,
        marginTop: 16
    },
    cardTopRightViewBirthday: {
        width: 36,
        height: 36,
        backgroundColor: color.LIGHT_YELLO,
        borderTopRightRadius: 8,
        borderBottomRightRadius: 8,
        justifyContent: 'center',
        alignItems: 'center'
    },
    mainViewStyles: {
        paddingHorizontal: 16,
        marginBottom: 41
    },
    mainViewHolidayStyles: {
        paddingHorizontal: 10,
        marginBottom: 20,
        // backgroundColor: 'rgba(0, 0, 0, 0.5)'
        // backgroundColor:'white'
    },
    mainViewHolidaytextStyles: {
        flex: 1,
        marginRight: 150,
        marginTop: 20
    },
    dullImageBackground:{
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
    }
});

export default styles;
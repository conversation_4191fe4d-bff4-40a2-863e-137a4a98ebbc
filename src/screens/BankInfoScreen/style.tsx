import { StyleSheet } from "react-native";
import { color } from '../../utils/constants/color'


const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 18,
    width: '96%',
    height: '100%',
    backgroundColor: color.WHITE,
    alignSelf: 'center',
    marginVertical: 10,
    gap: 10,
    borderRadius: 8,
  },
  title: {
    textAlign: 'center',
    fontSize: 14,
    color: color.ACCENT_BLUE
  },
  textLabel: {
    position: 'absolute',
    backgroundColor: color.WHITE,
    left: 10,
    top: -10,
    zIndex: 999,
    paddingHorizontal: 4,
    fontSize: 14,
    color: color.GREAYSCALE
  },
  infoField: {
    borderWidth: 0.5,
    borderColor: color.GREAYSCALE,
    padding: 14,
    borderRadius: 8,
    color: color.BLACK,
    // height: 50,
    maxHeight: 200,
    verticalAlign: 'middle'
  },
  dropdown: {
    height: 50,
    borderColor: color.GREAYSCALE,
    borderWidth: 0.5,
    borderRadius: 8,
    padding: 15,
  },
  icon: {
    marginRight: 5,
  },
  label: {
    position: 'absolute',
    backgroundColor: color.WHITE,
    left: 10,
    top: -10,
    zIndex: 999,
    paddingHorizontal: 4,
    fontSize: 14,
    color: color.GREAYSCALE

  },
  placeholderStyle: {
    fontSize: 16,
    color: color.BLACK
  },
  selectedTextStyle: {
    fontSize: 14,
    color: color.BLACK
  },
  iconStyle: {
    width: 20,
    height: 20,
  },
  inputSearchStyle: {
    height: 40,
    fontSize: 16,
  },
  unitView: {
    gap: 5,
    marginTop: 15
  }
})

export default styles
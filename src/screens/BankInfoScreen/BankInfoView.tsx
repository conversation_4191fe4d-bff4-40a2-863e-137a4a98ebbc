import React, { useState } from "react";
import { ScrollView, View } from "react-native";
import RNText from "../../component/RNText";
import styles from "./style";
import { Dropdown } from "react-native-element-dropdown";
import { color } from "../../utils/constants/color";
import { stringText } from "../../utils/constants/stringsText";


const accountTypeData = [
  { label: 'Saving Account', value: 0 },
  { label: 'Current Account', value: 1 },
];

interface BankInfoProps {
  bank_name: string | any;
  id_bank: string;
  branch_name: string;
  bank_branch_address_no1: string;
  bank_branch_address_no2: string;
  city: string;
  accountNumber: string;
  state: string | number | any;
  country: string| number | any;
  accountType: number | string | any;
  stateDropdown: any;
  countryDropdown: any;
  accountTypeDropdown: any;
}

const BankInfoView: React.FC<BankInfoProps> = ({
  bank_name,
  id_bank,
  branch_name,
  bank_branch_address_no1,
  bank_branch_address_no2,
  city,
  accountNumber,
  state,
  country,
  accountType,
  stateDropdown,
  countryDropdown,
}) => {
  const [isFocusState, setIsFocusState] = useState<Boolean>(false);
  const [isFocusCountry, setIsFocusCountry] = useState<Boolean>(false);
  const [isFocusAccountType, setIsFocusAccountType] = useState<Boolean>(false);

  const RenderStateLabel = () => {
    if (state || isFocusState) {
      return (
        <RNText style={[styles.label, isFocusState && { color: color.ACCENT_BLUE }]}>
          {stringText.State}
        </RNText>
      );
    }
    return null;
  };

  const RenderCountryLabel = () => {
    if (country || isFocusCountry) {
      return (
        <RNText style={[styles.label, isFocusCountry && { color: 'blue' }]}>
          {stringText.Country}
        </RNText>
      );
    }
    return null;
  };

  const RenderAccountTypeLabel = () => {
    if (accountType || isFocusAccountType || true) {
      return (
        <RNText style={[styles.label, isFocusAccountType && { color: 'blue' }]}>
          {stringText.AccountType}
        </RNText>
      );
    }
    return null;
  };

 

  return (
    <ScrollView>
      <View style={styles.container}>
        <View style={styles.unitView}>
          <RNText style={styles.textLabel}>{stringText.EmployeeBankName}</RNText>
          <RNText style={styles.infoField}>{bank_name ? bank_name : "NA"}</RNText>
        </View>

        <View style={styles.unitView}>
          <RNText style={styles.textLabel}>{stringText.IFSCCode}</RNText>
          <RNText style={styles.infoField}>{id_bank ? id_bank : "NA"}</RNText>
        </View>

        <View style={styles.unitView}>
          <RNText style={styles.textLabel}>{stringText.BankBranchAddressNo}</RNText>
          <RNText style={styles.infoField}>{bank_branch_address_no1 ? bank_branch_address_no1 : "NA"}</RNText>
        </View>

        <View style={styles.unitView}>
          <RNText style={styles.textLabel}>{stringText.BankBranchAddressNo2}</RNText>
          <RNText style={styles.infoField}>{bank_branch_address_no2 ? bank_branch_address_no2 : "NA"}</RNText>
        </View>

        <View style={styles.unitView}>
          <RNText style={styles.textLabel}>{stringText.BranchName}</RNText>
          <RNText style={styles.infoField}>{branch_name ? branch_name : "NA"}</RNText>
        </View>

        <View style={styles.unitView}>
          <RNText style={styles.textLabel}>{stringText.EmployeeAccountNumber}</RNText>
          <RNText style={styles.infoField}>{accountNumber ? accountNumber : "NA"}</RNText>
        </View>

        <View style={styles.unitView}>
          <RNText style={styles.textLabel}>{stringText.City}</RNText>
          <RNText style={styles.infoField}>{city ? city : "NA"}</RNText>
        </View>
        <View style={styles.unitView}>
          <View style={{ position: 'relative' }}>
            <RenderStateLabel />
            <Dropdown
              style={[styles.dropdown, isFocusState && { borderColor: color.DARK_BLUE }]}
              placeholderStyle={styles.placeholderStyle}
              selectedTextStyle={styles.selectedTextStyle}
              iconStyle={styles.iconStyle}
              data={stateDropdown.map((item: any) => ({ label: item.state_name, value: item.id }))}
              maxHeight={300}
              labelField="label"
              valueField="value"
              placeholder={!isFocusState ? 'State' : '...'}
              value={parseInt(state)}
              disable={true}
              onFocus={() => setIsFocusState(true)}
              onBlur={() => setIsFocusState(false)}
              onChange={item => {
                // setState(item.value);
                setIsFocusState(false);
              }}
            />
          </View>
        </View>
        <View style={styles.unitView}>
          <View style={{ position: 'relative' }}>
            <RenderCountryLabel />
            <Dropdown
              style={[styles.dropdown, isFocusCountry && { borderColor: color.DARK_BLUE }]}
              placeholderStyle={styles.placeholderStyle}
              selectedTextStyle={styles.selectedTextStyle}
              iconStyle={styles.iconStyle}
              data={countryDropdown.map((item : any) => ({label:item.country_name , value:item.id}) )}
              maxHeight={300}
              labelField="label"
              valueField="value"
              placeholder={!isFocusCountry ? 'Country' : '...'}
              value={country}
              disable={true}
              onFocus={() => setIsFocusCountry(true)}
              onBlur={() => setIsFocusCountry(false)}
              onChange={item => {
                // setCountry(item.value);
                setIsFocusCountry(false);
              }}
            />
          </View>
        </View>
        <View style={styles.unitView}>
          <View style={{ position: 'relative' }}>
            <RenderAccountTypeLabel />
            <Dropdown
              style={[styles.dropdown, isFocusAccountType && { borderColor: color.DARK_BLUE }]}
              placeholderStyle={styles.placeholderStyle}
              selectedTextStyle={styles.selectedTextStyle}
              iconStyle={styles.iconStyle}
              data={accountTypeData.map((item : any) => ({label: item.label, value: item.value }) )}
              maxHeight={300}
              labelField="label"
              valueField="value"
              placeholder={!isFocusAccountType ? 'Account Type' : '...'}
              value={accountType}
              disable={true}
              onFocus={() => setIsFocusAccountType(true)}
              onBlur={() => setIsFocusAccountType(false)}
              onChange={item => {
                // setAccountType(item.value);
                setIsFocusAccountType(false);
              }}
            />
          </View>
        
        </View>
      </View>
    </ScrollView>
  );
};

export default BankInfoView;

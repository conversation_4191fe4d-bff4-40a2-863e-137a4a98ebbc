import React, { SetStateAction } from 'react';
import {
    SafeAreaView,
    ScrollView,
    View
} from 'react-native';
import DatePicker from 'react-native-date-picker';
import moment from 'moment';
import Spinner from '../../component/Loader';
import { Table, Row, Rows } from 'react-native-table-component';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import styles from './styles';
import { stringText } from '../../utils/constants/stringsText';
import RNText from '../../component/RNText';
import RNImage from '../../component/RNImage';
import { AttendanceHistoryResponse } from '../../interfaces';
import RNButton from '../../component/RNButton';
import imageConstant from '../../utils/constants/imageConstant';

export type Props = {
    navigation: any;
    datesearch: Date;
    setOpenSearch: React.Dispatch<SetStateAction<boolean>>;
    datelast: Date;
    setOpenlast: React.Dispatch<SetStateAction<boolean>>;
    opensearch: boolean;
    setDateSearch: React.Dispatch<SetStateAction<Date>>;
    openlast: boolean;
    setDateLast: React.Dispatch<SetStateAction<Date>>;
    tableData: AttendanceHistoryResponse | any;
    fetching: boolean;
    errorHandlerVisibility: boolean;
    errorHandlerMessage: string;
    errorHandlerClicked: (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => void;
};

const AttendanceHistoryView = (props: Props) => {
    const {
        datesearch,
        setOpenSearch,
        datelast,
        setOpenlast,
        opensearch,
        setDateSearch,
        openlast,
        setDateLast,
        tableData,
        fetching,
        errorHandlerVisibility,
        errorHandlerMessage,
        errorHandlerClicked
    } = props;

    return (
        <SafeAreaView style={styles.mainView}>
            <ScrollView>
                <View style={styles.textOrder}>
                    <RNText style={styles.textK}>
                        {stringText.selectDate}
                    </RNText>
                </View>
                <View style={styles.ButtonImg}>
                    <RNButton
                        handleOnPress={() => setOpenSearch(true)}
                        style={[styles.datePickerWrapper]}
                    >
                        <RNText
                            children={`${moment(datesearch).format(
                                'DD/MM/YYYY'
                            )}`}
                            style={styles.inputText}
                        />
                        <RNImage
                            source={imageConstant.Calender}
                            style={styles.calenderView}
                            resizeMode='contain'
                        />
                    </RNButton>

                    <RNButton
                        handleOnPress={() => setOpenlast(true)}
                        style={[styles.datePickerWrapper]}
                    >
                        <RNText
                            children={`${moment(datelast).format(
                                'DD/MM/YYYY'
                            )}`}
                            style={styles.inputText}
                        />
                        <RNImage
                            source={imageConstant.Calender}
                            style={styles.calenderView}
                            resizeMode='contain'
                        />
                    </RNButton>
                </View>
                <DatePicker
                    modal
                    open={opensearch}
                    date={datesearch}
                    onConfirm={(date) => {
                        setOpenSearch(false);
                        setDateSearch(date);
                    }}
                    onCancel={() => {
                        setOpenSearch(false);
                    }}
                    mode="date"
                />
                <DatePicker
                    modal
                    open={openlast}
                    date={datelast}
                    onConfirm={(date) => {
                        setOpenlast(false);
                        setDateLast(date);
                    }}
                    onCancel={() => {
                        setOpenlast(false);
                    }}
                    mode="date"
                />
                {tableData?.tableData?.length > 0 && (
                    <View style={styles.TableView}>
                        <Table borderStyle={styles.TableBorder}>
                            <Row
                                data={tableData?.tableHead}
                                style={styles.head}
                                textStyle={styles.headText}
                            />
                            <Rows
                                data={tableData?.tableData}
                                textStyle={styles.text}
                            />
                        </Table>
                    </View>
                )}
                <ErrorHandlerPopup
                    visible={errorHandlerVisibility}
                    errorHandlerMessage={errorHandlerMessage}
                    errorHandlerClicked={errorHandlerClicked}
                />
            </ScrollView>
            {!(tableData?.tableData?.length > 0) && !fetching && (
                <View style={styles.notAvailableView}>
                    <RNText style={styles.DataAvaibleText}>
                        {stringText.DataNotAvailable}
                    </RNText>
                </View>
            )}
            <Spinner animating={fetching} />
        </SafeAreaView>
    );
};

export default AttendanceHistoryView;

import React, { useState } from 'react';
import {
    SafeAreaView,
    ScrollView,
    View,
} from 'react-native';
import styles from './styles';
import EmployeeCard from './EmpCard';
import { DRSType } from '.';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import Spinner from '../../component/Loader';

export type Props = {
    GoToLeavesScreen: (empId: string, name: string) => void;
    drsData: DRSType;
    fetching: boolean;
    errorHandlerVisibility: boolean;
    errorHandlerMessage: string;
    errorHandlerClicked: (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => void;
    scrollViewRef:any;
};

const EmployeeTreeView = (props: Props) => {
    const {
        GoToLeavesScreen,
        scrollViewRef,
        fetching,
        errorHandlerVisibility,
        errorHandlerMessage,
        errorHandlerClicked,
        drsData
    } = props;

    return (
        <SafeAreaView style={styles.mainView}>
            <ScrollView ref={scrollViewRef} contentContainerStyle={styles.scrollWrapper}>
                {drsData.managerDetails.employeeId && (
                    <EmployeeCard
                        GoToLeavesScreen={GoToLeavesScreen}
                        phone={drsData.managerDetails.phone}
                        email={drsData.managerDetails.email}
                        userId={drsData.managerDetails.userId}
                        designation={drsData.managerDetails.designation}
                        name={drsData.managerDetails.employeeName}
                        empId={drsData.managerDetails.employeeId}
                        profile={drsData.managerDetails.imagePath}
                        isAdmin={true}
                    />
                )}                
                {drsData.managerDetails.employeeId && (
                    <EmployeeCard
                        GoToLeavesScreen={GoToLeavesScreen}
                        phone={drsData.userDetails.phone}
                        userId={drsData.managerDetails.userId}
                        email={drsData.userDetails.email}
                        designation={drsData.userDetails.designation}
                        name={drsData.userDetails.employeeName}
                        empId={drsData.userDetails.employeeId}
                        profile={drsData.userDetails.imagePath}
                        isAdmin={true}
                    />
                )}
                {drsData.managerDetails.employeeId && (
                    <View style={styles.treeWrapper}>
                        <View style={styles.treeView}></View>
                        <View style={styles.cardsWrapper}>
                            {drsData.DRs.map((data, index) => (
                                <EmployeeCard
                                userId={data.userId}
                                    GoToLeavesScreen={GoToLeavesScreen}
                                    phone={data.phone}
                                    email={data.email}
                                    key={data.userId}
                                    designation={data.designation}
                                    name={data.employeeName}
                                    empId={data.employeeId}
                                    profile={data.imagePath}
                                    isAdmin={false}
                                />
                            ))}
                        </View>
                    </View>
                )}
            </ScrollView>
            <Spinner animating={fetching} />
            <ErrorHandlerPopup
                visible={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
        </SafeAreaView>
    );
};

export default EmployeeTreeView;

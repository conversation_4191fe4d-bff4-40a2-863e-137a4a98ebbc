import React from "react";
import { View } from "react-native";
import styles from "./styles";
import RNText from "../../component/RNText";
import FontAwesome from "react-native-vector-icons/FontAwesome"
import MaterialCommunityIcons from "react-native-vector-icons/MaterialCommunityIcons"
import { color } from "../../utils/constants/color";
import RNImage from "../../component/RNImage";
import { stringText } from "../../utils/constants/stringsText";
import RNButton from "../../component/RNButton";
const EmployeeCard: React.FC<{
    userId: string;
    isAdmin?: boolean;
    name: string;
    designation: string;
    empId: string;
    profile?: string;
    phone: string;
    email: string;
    GoToLeavesScreen: (userId: string, name: string) => void;
}> = ({ isAdmin, name, designation, empId, userId, profile, phone, email, GoToLeavesScreen }) => {
    return (
        <RNButton
            ActiveOpacity={isAdmin ? 1 : 0.8}
            handleOnPress={() => !isAdmin ? GoToLeavesScreen(userId.toString(), name)
                : ''}
            style={{ position: 'relative', zIndex: 1 }}
        >
            <View style={styles.empCardWrapper}>
                <View style={styles.proileImgWrapper}>
                    {profile && (
                        <RNImage
                            source={{ uri: profile }}
                            style={styles.proileImg}
                        />
                    )}
                </View>
                <View style={{ gap: 6,width:"75%" }}>
                    <RNText style={styles.nameText}>{name}</RNText>
                    <RNText style={styles.designationText}>
                        {stringText.EmpId}: {empId}
                    </RNText>
                    <RNText style={styles.designationText}>
                        {designation}
                    </RNText>
                    <View style={styles.iconDetailsWrapper}>
                        <FontAwesome
                            name="phone"
                            size={16}
                            color={color.BLACK + 60}
                        />
                        <RNText style={styles.contactText}>
                            {phone}
                        </RNText>
                    </View>
                    <View style={styles.iconDetailsWrapper}>
                        <MaterialCommunityIcons
                            name="email"
                            size={16}
                            color={color.BLACK + 60}
                        />
                        <RNText style={[styles.contactText,]}>
                            {email}
                        </RNText>
                    </View>
                </View>
            </View>
            {isAdmin && Number(empId) < 10 ? (
                <View style={styles.bottomLine}></View>
            ) : (
                !isAdmin && <View style={styles.leftLine}></View>
            )}
        </RNButton>
    );
};


export default EmployeeCard
import React, { useEffect, useRef, useState } from 'react';
import EmployeeTreeView from './EmployeeTreeView';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import { httpGet } from '../../utils/http';
import { stringText } from '../../utils/constants/stringsText';
import apiConstant from '../../utils/constants/apiConstant';

export type Props = {
    navigation: any
}


export type DRSType={
    managerDetails: {
        phone: string;
        email: string;
        userId:string,
        designation: string;
        employeeName: string;
        employeeId: string;
        imagePath: string;
    };
    userDetails: {
        phone: string;
        email: string;
        userId:string,
        designation: string;
        employeeName: string;
        employeeId: string;
        imagePath: string;
    };
    DRs: {
        phone: string;
        userId: string;
        email: string;
        designation: string;
        employeeName: string;
        employeeId: string;
        imagePath: string;
    }[];
};


const EmplyeeTreeScreen = (props: Props) => {
    const { navigation } = props;
    const scrollViewRef = useRef();


    const GoToLeavesScreen = (userId:string,name:string) => {
        navigation.navigate(navigationStringText.DRsDeatils,{userId:userId,name:name});
    };

    // const GoToLeavesScreen = (userId:string,name:string) => {
    //     navigation.navigate(navigationStringText.LeavesDetails,{userId:userId,name:name});
    // };



    const [fetching, setFetching] = useState<boolean>(false);
    const [drsData,setDrsData] = useState<DRSType>({managerDetails: {
            phone: "",
            email: "",
            userId:'',
            designation: "",
            employeeName: "",
            employeeId: "",
            imagePath: "",
        },
        userDetails: {
            phone: "",
            userId:'',
            email: "",
            designation: "",
            employeeName: "",
            employeeId: "",
            imagePath: "",
        },
        DRs: [{
            phone: "",
            userId: "",
            email: "",
            designation:"",
            employeeName: "",
            employeeId: "",
            imagePath: "",
        }]
    })
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);



    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        setFetching(true);
        httpGet(apiConstant.DRs_LIST)
            .then((response: any) => {
                const drsData =
                    JSON.parse(response)?.data
                setDrsData(drsData)
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.responsesetDrsData?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });

        const unsubscribe = navigation.addListener('blur', () => {
            if (scrollViewRef.current) {
                scrollViewRef.current.scrollTo({ y: 0, animated: false });
            }
        });

        return unsubscribe;
    }, []);


    return (
        <EmployeeTreeView
            scrollViewRef={scrollViewRef}
            GoToLeavesScreen={GoToLeavesScreen}
            drsData={drsData}
            fetching={fetching}
            errorHandlerVisibility={errorHandlerVisibility}
            errorHandlerMessage={errorHandlerMessage}
            errorHandlerClicked={errorHandlerClicked}
        />
    );
};

export default EmplyeeTreeScreen;

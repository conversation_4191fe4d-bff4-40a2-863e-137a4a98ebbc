import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

const styles = StyleSheet.create({
    mainView: {
        flex: 1
    },
    scrollWrapper: {
        padding: 16,
        flexGrow:1
    },
    treeWrapper: {
        flexDirection: 'row',
        gap: 20,
        marginLeft: 20
    },
    treeView: {
        height: '100%',
        width: 4,
        backgroundColor: color.BLACK,
        marginTop: -92,
        opacity:0.2
    },
    cardsWrapper: { flex: 1, position: 'relative', zIndex: 10 },
    bottomLine: {
        width: 4,
        height: 40,
        backgroundColor: color.BLACK ,
        position: 'absolute',
        bottom: -20,
        left: '50%',
        opacity:0.2
    },
    leftLine: {
        width: 40,
        height: 4,
        backgroundColor: color.BLACK ,
        position: 'absolute',
        bottom: '50%',
        left: -24,
        opacity:0.2
    },
    empCardWrapper: {
        zIndex: 10,
        position: 'relative',
        width: '100%',
        shadowColor: color.BLACK,
        marginBottom: 20,
        flexDirection: 'row',
        alignItems: 'center',
        gap: 20,
        elevation: 2,
        padding: 20,
        borderRadius: 6,
        backgroundColor: color.OFF_WHITE
    },
    proileImgWrapper: {
        width: "25%",
        aspectRatio:1/1,
        borderRadius: 200,
        overflow: 'hidden',
        elevation: 2,
        backgroundColor: color.OFF_WHITE
    },
    proileImg: {
        width: '100%',
        height: '100%',
        backgroundColor:color.WHITE
    },
    nameText: {
        fontSize: 18,
        color: color.BLACK
    },
    designationText: {
        fontSize: 14,
        color: color.BLACK,
        fontStyle: 'italic',
        width:'95%',
    },
    contactText: {
        fontSize: 14,
        color: color.BLACK,
        fontStyle: 'italic',
        width:'85%',
    },
    iconDetailsWrapper:{
        flexDirection:'row',
        alignItems:'center',
        gap:6
    }
});

export default styles;

import React, { useEffect, useState } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, SafeAreaView, View } from 'react-native';
import RNText from '../../component/RNText';
import styles from './style';
import HTMLViewIDSR from './HTMLRenderIDSR';
import { stringText } from '../../utils/constants/stringsText';
import RNButton from '../../component/RNButton';

const getAllMonth = [
    { label: 'Jan', value: 1 },
    { label: 'Feb', value: 2 },
    { label: 'Mar', value: 3 },
    { label: 'April', value: 4 },
    { label: 'May', value: 5 },
    { label: 'June', value: 6 },
    { label: 'July', value: 7 },
    { label: 'Aug', value: 8 },
    { label: 'Sept', value: 9 },
    { label: 'Oct', value: 10 },
    { label: 'Nov', value: 11 },
    { label: 'Dec', value: 12 }
];

const IDSRListCard: React.FC<{
    timeSpent: string;
    projectName: string;
    subject: string;
    date: string;
    otherProjectName: string;
    body: string;
}> = ({ timeSpent, projectName, subject, date, body, otherProjectName }) => {
    const dateObj = new Date(date);
    const DateDay = `${dateObj.getDate()}`;
    const DateMonth = `${dateObj.getMonth() + 1}`;
    const daysOfWeek = [
        'Sunday',
        'Monday',
        'Tuesday',
        'Wednesday',
        'Thursday',
        'Friday',
        'Saturday'
    ];
    const dayIndex = dateObj.getDay();
    const dayOfWeek = daysOfWeek[dayIndex];
    const formatDate = (dateString: string): string => {
        const date = new Date(dateString);
        const options: Intl.DateTimeFormatOptions = {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric',
            weekday: 'long'
        };
        const formattedDate = new Intl.DateTimeFormat('en-US', options).format(
            date
        );
        const [day, datePart] = formattedDate.split(',');
        const formattedResult = `${datePart.trim()} (${day.trim()})`;

        return formattedResult;
    };
    const [idsrPopup, setIdsrPopup] = useState<boolean>(false);
    const handleIDSRPopup = () => {
        setIdsrPopup(!idsrPopup);
    };
    const [projectNameIfOther, setProjectName] = useState<string | null>(
        projectName
    );
    useEffect(() => {
        if (otherProjectName) {
            setProjectName(otherProjectName);
        }
    }, [projectName]);
    return (
        <SafeAreaView style={styles.container}>
            <RNButton
                ActiveOpacity={0.6}
                handleOnPress={handleIDSRPopup}
            >
                <View style={styles.cardView}>
                    <View style={styles.cardUpperView}>
                        <RNText style={styles.projectName}>
                            {projectNameIfOther ? projectNameIfOther : "---"}
                        </RNText>
                        <RNText style={styles.dateField}>
                            {formatDate(date) ? formatDate(date) : "---"}
                        </RNText>
                    </View>
                    <View style={styles.cardLowerView}>
                        <View>
                            <RNText style={styles.subTimeHeading}>
                                {stringText.SUBJECT}
                            </RNText>
                            <RNText style={styles.idsrSubject}>
                                {subject ? subject : "---"}
                            </RNText>
                        </View>
                        <View
                        // style={styles.lowerRightView}
                        >
                            <RNText style={[styles.subTimeHeading, { textAlign: 'right' }]}>
                                {stringText.TIMESPENTMINS}
                            </RNText>
                            <RNText style={styles.idsrSubject}>
                                {timeSpent ? timeSpent : "---"}
                            </RNText>
                        </View>
                    </View>
                </View>
            </RNButton>
            <HTMLViewIDSR
                timeSpent={timeSpent ? timeSpent : "---"}
                isVisible={idsrPopup}
                onConfirm={handleIDSRPopup}
                body={body}
                subject={subject}
            />
        </SafeAreaView>
    );
};

export default IDSRListCard;

import React, { useEffect, useState } from 'react';
import { SafeAreaView, View, FlatList } from 'react-native';
import { httpGet } from '../../utils/http';
import { stringText } from '../../utils/constants/stringsText';
import RNActivityIndicator from '../../component/Loader';
import IDSRListCard from './IDSRCard';
import { Dropdown } from 'react-native-element-dropdown';
import styles from './style';
import { color } from '../../utils/constants/color';
import RNText from '../../component/RNText';
import apiConstant from '../../utils/constants/apiConstant';

export type Props = {
    navigation: any;
    route: any;
};

export type IDSRType = {
    IDSR_date: string;
    subject: string;
    ISO_day_no: number;
    ISO_week_no: string;
    project_name: string;
    body: string;
    time_spent: string;
    other_project_name: string;
};
export interface MonthData {
    id: number;
    start_date: string;
}

const TimeSheetScreen = (props: Props) => {
    const [fetching, setFetching] = useState<boolean>(false);
    const [userId, setUserId] = useState<number | null>(props?.route?.params?.userId);
    const [monthTimesheet, setMonthTimesheet] = useState<MonthData[]>([]);
    const [monthID, setMonthId] = useState();
    const [timesheetData, setTimesheetData] = useState<IDSRType[]>([]);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [monthData, setMonthData] = useState<MonthData[]>([]);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        if (!(props?.route?.params?.userId)) {
            fetchUserId();
        }
        else if (userId) {
            fetchMonth();
        }
    }, []);

    useEffect(() => {
        if (userId) {
            fetchMonth();
        }
    }, [userId]);

    useEffect(() => {
        if (monthID) {
            fetchIDSRs();
        }
    }, [monthID]);

    const fetchUserId = () => {
        setFetching(true);
        httpGet(apiConstant.USER_INFO)
            .then((response: any) => {
                const UserId = JSON.parse(response)?.data;
                if (UserId) {
                    setUserId(UserId?.id);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const fetchMonth = () => {
        setFetching(true);
        httpGet(`${apiConstant.ATTENDANCE_TIMESHEET}?userId=${userId}`)
            .then((response: any) => {
                const timeSheet = JSON.parse(response)?.data;
                if (timeSheet) {
                    setMonthTimesheet(timeSheet);
                    setMonthId(timeSheet[0].id);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const fetchIDSRs = () => {
        setFetching(true);
        httpGet(`${apiConstant.IDSR}?userId=${userId}&tId=${monthID}`)
            .then((response: any) => {
                const IdsrData = JSON.parse(response)?.data;
                if (IdsrData) {
                    setTimesheetData(IdsrData);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const handleMonthChange = (month: any) => {
        setMonthId(month);
    };

    const filterByMonth = (idsr: IDSRType) => {
        if (!monthID) return true;
        const idsrMonth = new Date(idsr.IDSR_date).getMonth() + 1;
        const selectedMonth = parseInt(monthID);
        return idsrMonth === selectedMonth;
    };

    const renderIDSRItem = ({ item }: { item: IDSRType }) => {
        return (
            <IDSRListCard
                timeSpent={item.time_spent}
                projectName={item.project_name}
                subject={item.subject}
                date={item.IDSR_date}
                body={item.body}
                otherProjectName={item.other_project_name}
            />
        );
    };

    const getMonthName = (dateString: string): string => {
        const date = new Date(dateString);
        const monthIndex = date.getMonth();
        const year = date.getFullYear();
        const months = [
            'January',
            'February',
            'March',
            'April',
            'May',
            'June',
            'July',
            'August',
            'September',
            'October',
            'November',
            'December'
        ];
        return `${months[monthIndex]} ${year}`;
    };
    console.log("timesheetData",timesheetData);
    return (
        <SafeAreaView
            style={styles.mainContainer}
        >
            <View style={styles.searchView}>
                <Dropdown
                    placeholder={monthID ? monthID : 'Select Month'}
                    style={styles.dropDown}
                    placeholderStyle={{ color: color.BLACK }}
                    itemTextStyle={{ color: color.BLACK }}
                    selectedTextStyle={{ color: color.BLACK }}
                    data={monthTimesheet.map((month: any) => ({
                        label: getMonthName(month.start_date),
                        value: month.id
                    }))}
                    value={monthID}
                    labelField="label"
                    valueField="value"
                    onChange={(value: any) => handleMonthChange(value.value)}
                />
            </View>
            {timesheetData.length > 0 && !fetching ? (
                <FlatList
                    data={timesheetData}
                    renderItem={renderIDSRItem}
                    keyExtractor={(item) => item.IDSR_date.toString()}
                    contentContainerStyle={{ paddingBottom: 30 }}
                    showsVerticalScrollIndicator={false}
                />
            ) : (
                !fetching && (
                    <View
                        style={styles.noDataView}
                    >
                        <RNText style={styles.noDataText}>{stringText.DataNotAvailable}</RNText>
                    </View>
                )
            )}
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    );
};

export default TimeSheetScreen;

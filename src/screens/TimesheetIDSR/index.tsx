import React, { useEffect, useState } from 'react';
import { SafeAreaView, View, FlatList, TouchableOpacity, Modal } from 'react-native';
import { httpGet } from '../../utils/http';
import { stringText } from '../../utils/constants/stringsText';
import RNActivityIndicator from '../../component/Loader';
import { Dropdown } from 'react-native-element-dropdown';
import styles from './style';
import { color } from '../../utils/constants/color';
import RNText from '../../component/RNText';
import apiConstant from '../../utils/constants/apiConstant';
import EditTimesheetModal from './EditTimesheet';
import { useNavigation } from '@react-navigation/native';
import Ionicons from 'react-native-vector-icons/Ionicons';

export type Props = {
    navigation: any;
    route: any;
};

export type IDSRType = {
    IDSR_date: string;
    subject: string;
    ISO_day_no?: number;
    ISO_week_no?: string;
    project_name: string;
    body: string;
    time_spent: string;
    other_project_name?: string;
    is_draft: number;
    id?: number; 
    submission_time?: string;
    mandate_type_id?: number;
    status_id?: number;
};

export interface MonthData {
    id: number;
    start_date: string;
}

const TimeSheetScreen = (props: Props) => {
    const navigation = useNavigation<any>();
    const [fetching, setFetching] = useState(false);
    const [userId, setUserId] = useState<number | null>(props?.route?.params?.userId);
    const [monthTimesheet, setMonthTimesheet] = useState<MonthData[]>([]);
    const [monthID, setMonthId] = useState();
    const [timesheetData, setTimesheetData] = useState<IDSRType[]>([]);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState(false);
    const [mandateTypes, setMandateTypes] = useState<{ id: number; mandate_name: string }[]>([]);
    const [statusOptions, setStatusOptions] = useState<{ id: number; status_name: string }[]>([]);
    const [selectedTimesheet, setSelectedTimesheet] = useState<IDSRType | null>(null);
    const [showTimesheetModal, setShowTimesheetModal] = useState(false);


    const errorHandlerClicked = (visibility: boolean, message: string) => {
        setErrorHandlerVisibility(visibility);
        setErrorHandlerMessage(message);
    };

    useEffect(() => {
        console.log('Timesheet component mounted/refreshed');
        initializeData();
        fetchMandateTypes();
        fetchStatusOptions();
    }, []); 
    

    const initializeData = () => {
        if (!props?.route?.params?.userId) {
            fetchUserId();
        } else if (userId) {
            fetchMonth();
        }
    };

    useEffect(() => {
        if (userId) fetchMonth();
    }, [userId]);

    useEffect(() => {
        if (monthID) fetchIDSRs();
    }, [monthID]);

    const fetchUserId = () => {
        setFetching(true);
        httpGet(apiConstant.USER_INFO)
            .then((response: any) => {
                const UserId = JSON.parse(response)?.data;
                if (UserId) setUserId(UserId?.id);
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
            });
    };

    const fetchMonth = () => {
        setFetching(true);
        httpGet(`${apiConstant.ATTENDANCE_TIMESHEET}?userId=${userId}`)
            .then((response: any) => {
                const timeSheet = JSON.parse(response)?.data;
                if (timeSheet?.length) {
                    setMonthTimesheet(timeSheet);
                    setMonthId(timeSheet[0].id);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
            });
    };

    const fetchIDSRs = () => {
        setFetching(true);
        httpGet(`${apiConstant.IDSR}?userId=${userId}&tId=${monthID}`)
            .then((response: any) => {
                const IdsrData = JSON.parse(response)?.data;
                if (IdsrData) setTimesheetData(IdsrData);
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
            });
    };

    const handleMonthChange = (month: any) => {
        setMonthId(month);
    };

    const formatDateWithDay = (dateString: string): string => {
        const date = new Date(dateString);
        const day = date.getDate().toString().padStart(2, '0');
        const month = (date.getMonth() + 1).toString().padStart(2, '0');
        const year = date.getFullYear();
        const daysOfWeek = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
        const dayName = daysOfWeek[date.getDay()];
        return `${month}/${day}/${year} (${dayName})`;
    };

    const getMonthName = (dateString: string): string => {
        const date = new Date(dateString);
        const monthIndex = date.getMonth();
        const year = date.getFullYear();
        const months = [
            'January',
            'February',
            'March',
            'April',
            'May',
            'June',
            'July',
            'August',
            'September',
            'October',
            'November',
            'December'
        ];
        return `${months[monthIndex]} ${year}`;
    };


    const formatTimeSpent = (timeSpent: string | number): string => {
        if (!timeSpent || timeSpent === '---') {
            return '---';
        }
        const minutes = parseInt(timeSpent.toString());
        if (isNaN(minutes)) {
            return '---';
        }
        const hours = minutes / 60;
        const formattedHours = hours.toFixed(1);
        return `${minutes} mins (${formattedHours} hrs)`;
    };

    const formatSubmissionTime = (timestamp: string | undefined): string => {
        if (!timestamp) return '---';
    
        const date = new Date(timestamp);
        const hours = date.getHours();
        const minutes = date.getMinutes();
    
        
        const hours12 = hours % 12 || 12;
        const ampm = hours >= 12 ? 'PM' : 'AM';
    
        const formattedTime = `${hours12.toString().padStart(2, '0')}:${minutes
            .toString()
            .padStart(2, '0')} ${ampm}`;
    
        return formattedTime;
    };

    const fetchMandateTypes = async () => {
        try {
            const response = await httpGet(apiConstant.MANDATE_TYPES);
            const parsed = JSON.parse(response);
            if (parsed && parsed.data && Array.isArray(parsed.data.data)) {
                setMandateTypes(parsed.data.data);
            }
        } catch (error) {
            console.log('Error fetching mandate types:', error);
        }
    };

    const getMandateTypeName = (mandateTypeId: number | undefined): string => {
        if (!mandateTypeId) return '---';
        
        const mandateType = mandateTypes.find(type => type.id === mandateTypeId);
        return mandateType ? mandateType.mandate_name : '---';
    };

    const fetchStatusOptions = async () => {
        try {
            const response = await httpGet(apiConstant.STATUS);
            const parsed = JSON.parse(response);
            if (parsed && Array.isArray(parsed.data)) {
                setStatusOptions(parsed.data);
            }
        } catch (error) {
            console.log('Error fetching status options:', error);
        }
    };

    const getStatusName = (statusId: number | undefined): string => {
        if (!statusId) return '---';
        
        const status = statusOptions.find(s => s.id === statusId);
        return status ? status.status_name : '---';
    };
    

    const renderIDSRItem = ({ item }: { item: IDSRType }) => {
        return (
            <TouchableOpacity 
                style={styles.timesheetCard}
                onPress={() => {
                    console.log('Timesheet clicked:', item);
                    openTimesheetModal(item);
                }}
                activeOpacity={0.7}
            >
                <View style={styles.timesheetCardHeader}>
                    <RNText style={styles.timesheetCardDate}>{formatDateWithDay(item.IDSR_date)}</RNText>
                    <TouchableOpacity
                        onPress={(e) => {
                            e.stopPropagation(); 
                            if (item.is_draft === 1) {
                                navigation.navigate('EditTimesheetScreen', { timesheetId: item.id });
                            }
                        }}
                    >
                        <RNText style={{ color: item.is_draft === 1 ? 'blue' : 'green' }}>
                            {item.is_draft === 1 ? '✏️' : '✅'}
                        </RNText>
                    </TouchableOpacity>
                </View>
                <View style={styles.timesheetHeaderDivider} />
                
                <View style={styles.timesheetTableRow}>
                    <View style={styles.timesheetTableHeader}>
                        <RNText style={styles.timesheetTableHeaderText}>Subject:</RNText>
                    </View>
                    <View style={styles.timesheetTableContent}>
                        <RNText style={styles.timesheetDetail}>{item.subject}</RNText>
                    </View>
                </View>

                <View style={styles.timesheetTableRow}>
                    <View style={styles.timesheetTableHeader}>
                        <RNText style={styles.timesheetTableHeaderText}>Project Name:</RNText>
                    </View>
                    <View style={styles.timesheetTableContent}>
                        <RNText style={styles.timesheetDetail}>{item.project_name}</RNText>
                    </View>
                </View>

                {item.other_project_name ? (
                    <View style={styles.timesheetTableRow}>
                        <View style={styles.timesheetTableHeader}>
                            <RNText style={styles.timesheetTableHeaderText}>Other Project:</RNText>
                        </View>
                        <View style={styles.timesheetTableContent}>
                            <RNText style={styles.timesheetDetail}>{item.other_project_name}</RNText>
                        </View>
                    </View>
                ) : null}

                <View style={styles.timesheetTableRow}>
                    <View style={styles.timesheetTableHeader}>
                        <RNText style={styles.timesheetTableHeaderText}>Type:</RNText>
                    </View>
                    <View style={styles.timesheetTableContent}>
                        <RNText style={styles.timesheetDetail}>{getMandateTypeName(item.mandate_type_id)}</RNText>
                    </View>
                </View>

                <View style={styles.timesheetTableRow}>
                    <View style={styles.timesheetTableHeader}>
                        <RNText style={styles.timesheetTableHeaderText}>Status:</RNText>
                    </View>
                    <View style={styles.timesheetTableContent}>
                        <RNText style={styles.timesheetDetail}>{getStatusName(item.status_id)}</RNText>
                    </View>
                </View>

                <View style={styles.timesheetTableRow}>
                    <View style={styles.timesheetTableHeader}>
                        <RNText style={styles.timesheetTableHeaderText}>Time Spent:</RNText>
                    </View>
                    <View style={styles.timesheetTableContent}>
                        <RNText style={styles.timesheetDetail}>{formatTimeSpent(item.time_spent)}</RNText>
                    </View>
                </View>

                <View style={styles.timesheetTableRow}>
                    <View style={styles.timesheetTableHeader}>
                        <RNText style={styles.timesheetTableHeaderText}>Submitted At:</RNText>
                    </View>
                    <View style={styles.timesheetTableContent}>
                        <RNText style={styles.timesheetDetail}>
                            {item.is_draft === 1 ? '---' : formatSubmissionTime(item.submission_time)}
                        </RNText>
                    </View>
                </View>

            </TouchableOpacity>
        );
    };

    const openTimesheetModal = (timesheet: IDSRType) => {
        setSelectedTimesheet(timesheet);
        setShowTimesheetModal(true);
    };

    const closeTimesheetModal = () => {
        setShowTimesheetModal(false);
        setSelectedTimesheet(null);
    };

    const extractTaskDescriptionFromTable = (htmlBody: string) => {
        if (!htmlBody) return '';
        
        try {
            
            const taskDescriptionMatch = htmlBody.match(/Task Description.*?<\/th>.*?<td[^>]*>.*?<p>(.*?)<\/p>.*?<\/td>/s);
            if (taskDescriptionMatch && taskDescriptionMatch[1]) {
                return taskDescriptionMatch[1]
                    .replace(/<[^>]*>/g, '') 
                    .replace(/&nbsp;/g, ' ') 
                    .replace(/&amp;/g, '&') 
                    .replace(/&lt;/g, '<') 
                    .replace(/&gt;/g, '>') 
                    .replace(/&quot;/g, '"') 
                    .replace(/&#39;/g, "'") 
                    .trim(); 
            }
        } catch (error) {
            console.log('Error parsing HTML table:', error);
        }
        
        return '';
    };

    const extractMultipleSectionsFromTable = (htmlBody: string) => {
        if (!htmlBody) return [];
        
        try {
            const sections = [];
            
            const rowMatches = htmlBody.match(/<tr[^>]*>.*?<\/tr>/gs);
            
            if (rowMatches) {
                rowMatches.forEach((row, index) => {
                    if (row.includes('<th>')) {
                        return;
                    }
                    
                    const tdMatches = row.match(/<td[^>]*>(.*?)<\/td>/gs);
                    
                    if (tdMatches && tdMatches.length >= 6) {
                        const projectName = tdMatches[0].replace(/<td[^>]*>(.*?)<\/td>/s, '$1').replace(/<[^>]*>/g, '').trim();
                        const mandateType = tdMatches[1].replace(/<td[^>]*>(.*?)<\/td>/s, '$1').replace(/<[^>]*>/g, '').trim();
                        const taskDescription = tdMatches[2].replace(/<td[^>]*>(.*?)<\/td>/s, '$1').replace(/<[^>]*>/g, '').trim();
                        const taskStatus = tdMatches[3].replace(/<td[^>]*>(.*?)<\/td>/s, '$1').replace(/<[^>]*>/g, '').trim();
                        const priority = tdMatches[4].replace(/<td[^>]*>(.*?)<\/td>/s, '$1').replace(/<[^>]*>/g, '').trim();
                        const timeSpent = tdMatches[5].replace(/<td[^>]*>(.*?)<\/td>/s, '$1').replace(/<[^>]*>/g, '').trim();
                        
                        if (projectName || mandateType || taskDescription) {
                            sections.push({
                                projectName: projectName || '---',
                                mandateType: mandateType || '---',
                                taskDescription: taskDescription || '---',
                                taskStatus: taskStatus || '---',
                                priority: priority || 'High',
                                timeSpent: timeSpent || '---'
                            });
                        }
                    }
                });
            }
            
            if (sections.length === 0) {
                const taskDescription = extractTaskDescriptionFromTable(htmlBody);
                sections.push({
                    projectName: selectedTimesheet?.project_name || '---',
                    mandateType: getMandateTypeName(selectedTimesheet?.mandate_type_id),
                    taskDescription: taskDescription,
                    taskStatus: getStatusName(selectedTimesheet?.status_id),
                    priority: 'High',
                    timeSpent: selectedTimesheet?.time_spent || '---'
                });
            }
            
            return sections;
        } catch (error) {
            console.log('Error parsing HTML table sections:', error);
            
            const taskDescription = extractTaskDescriptionFromTable(htmlBody);
            return [{
                projectName: selectedTimesheet?.project_name || '---',
                mandateType: getMandateTypeName(selectedTimesheet?.mandate_type_id),
                taskDescription: taskDescription,
                taskStatus: getStatusName(selectedTimesheet?.status_id),
                priority: 'High',
                timeSpent: selectedTimesheet?.time_spent || '---'
            }];
        }
    };

    const renderTimesheetModal = () => {
        if (!selectedTimesheet) {
            return null;
        }

        const totalTimeSpent = parseInt(selectedTimesheet.time_spent) || 0;
        const totalHours = (totalTimeSpent / 60).toFixed(1);

        const sections = extractMultipleSectionsFromTable(selectedTimesheet.body || '');

        return (
            <Modal
                visible={showTimesheetModal}
                animationType="slide"
                transparent={true}
                onRequestClose={closeTimesheetModal}
            >
                <View style={styles.modalOverlay}>
                    <View style={styles.modalContainer}>
                        <View style={styles.modalHeader}>
                            <View style={styles.modalTitleContainer}>
                                <RNText style={styles.modalTitle}>
                                    TIMESHEET {formatDateWithDay(selectedTimesheet.IDSR_date)}
                                </RNText>
                                <RNText style={styles.modalSubtitle}>
                                    Total time spent: {totalTimeSpent} mins ({totalHours} hrs)
                                </RNText>
                            </View>
                            <TouchableOpacity 
                                style={styles.modalCloseButton}
                                onPress={closeTimesheetModal}
                            >
                                <Ionicons name="close" size={24} color="#333" />
                            </TouchableOpacity>
                        </View>

                        <View style={styles.modalTableContainer}>
                            <View style={styles.modalTableHeader}>
                                <RNText style={styles.modalTableHeaderText}>Project Name</RNText>
                                <RNText style={styles.modalTableHeaderText}>Mandate Type</RNText>
                                <RNText style={styles.modalTableHeaderText}>Task Description</RNText>
                                <RNText style={styles.modalTableHeaderText}>Task Status</RNText>
                                <RNText style={styles.modalTableHeaderText}>Priority</RNText>
                                <RNText style={styles.modalTableHeaderText}>Time Spent (mins)</RNText>
                            </View>

                            {sections.map((section, index) => (
                                <View key={index} style={styles.modalTableRow}>
                                    <RNText style={styles.modalTableCell}>
                                        {section.projectName}
                                    </RNText>
                                    <RNText style={styles.modalTableCell}>
                                        {section.mandateType}
                                    </RNText>
                                    <RNText style={styles.modalTableCell}>
                                        {section.taskDescription}
                                    </RNText>
                                    <RNText style={styles.modalTableCell}>
                                        {section.taskStatus}
                                    </RNText>
                                    <RNText style={styles.modalTableCell}>
                                        {section.priority}
                                    </RNText>
                                    <RNText style={styles.modalTableCell}>
                                        {section.timeSpent}
                                    </RNText>
                                </View>
                            ))}
                        </View>
                    </View>
                </View>
            </Modal>
        );
    };

    return (
        <SafeAreaView style={styles.mainContainer}>
            <View style={styles.searchView}>
                <Dropdown
                    placeholder={monthID ? monthID : 'Select Month'}
                    style={styles.dropDown}
                    placeholderStyle={{ color: color.BLACK }}
                    itemTextStyle={{ color: color.BLACK }}
                    selectedTextStyle={{ color: color.BLACK }}
                    data={monthTimesheet.map((month: any) => ({
                        label: getMonthName(month.start_date),
                        value: month.id
                    }))}
                    value={monthID}
                    labelField="label"
                    valueField="value"
                    onChange={(value: any) => handleMonthChange(value.value)}
                />
            </View>
            {timesheetData.length > 0 && !fetching ? (
                <FlatList
                    data={timesheetData}
                    renderItem={renderIDSRItem}
                    keyExtractor={(item, index) => `${item.IDSR_date}-${index}`}
                    contentContainerStyle={{ paddingBottom: 100 }}
                    showsVerticalScrollIndicator={false}
                />
            ) : (
                !fetching && (
                    <View style={styles.noDataView}>
                        <RNText style={styles.noDataText}>{stringText.DataNotAvailable}</RNText>
                    </View>
                )
            )}            
            <RNActivityIndicator animating={fetching} />
            {renderTimesheetModal()}
        </SafeAreaView>
    );
};

export default TimeSheetScreen;
import React, { useEffect, useState } from 'react';
import RenderHTML from 'react-native-render-html';
import {
    View,
    Modal,
    TouchableWithoutFeedback,
    useWindowDimensions,
} from 'react-native';
import { color } from '../../utils/constants/color';
import { ScrollView } from 'react-native-gesture-handler';
import Ionicons from 'react-native-vector-icons/Ionicons';
import styles from './style';
import RNButton from '../../component/RNButton';
import RNText from '../../component/RNText';
import { stringText } from '../../utils/constants/stringsText';

interface Props {
    timeSpent: string;
    isVisible: boolean;
    onConfirm: () => void;
    body: string;
    subject: string;
}

const HTMLViewIDSR: React.FC<Props> = ({
    timeSpent,
    body,
    isVisible,
    subject,
    onConfirm
}) => {
    const toggleModal = () => {
        onConfirm();
    };
    const modifiedHTML = updateColumnWidths(body);

    function updateColumnWidths(html: string): string {
        return html.replace(/<tr>([\s\S]*?)<\/tr>/g, (rowMatch) => {
            let colIndex = 0;

            return rowMatch.replace(/<(td|th)([^>]*)>/g, (match, tag, attrs) => {
                colIndex += 1;
                const width = colIndex === 3 ? 350 : 120;
                if (/style=/.test(attrs)) {
                    return `<${tag}${attrs.replace(/style="(.*?)"/, `style="width: ${width}px; $1"`)}>`;
                } else {
                    return `<${tag} style="width: ${width}px;"${attrs}>`;
                }
            });
        });
    }

    return (
        <Modal
            onRequestClose={() => {
                toggleModal();
            }}
            transparent={true}
            visible={isVisible}
        >

            <View
                style={styles.timesheetParentView}
            >

                <View
                    style={styles.timesheetInnerView}
                >
                    <ScrollView
                        style={styles.scrollContainer}
                    >
                        <View style={styles.subjectConatiner}>
                            <View>
                                <RNText style={styles.subjectTitle}>
                                    {subject}
                                </RNText>
                                <RNText style={styles.subTimeHeading}>
                                    {stringText.TOTALTIMESPENT}{timeSpent}{stringText.MINS}
                                </RNText>
                            </View>
                            <RNButton
                                handleOnPress={toggleModal}
                                style={styles.crossBtn}
                            >
                                <Ionicons name="close-outline" size={28} color="black" />
                            </RNButton>
                        </View>
                        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
                            <RenderHTML
                                contentWidth={1000}
                                source={{ html: modifiedHTML }}
                                tagsStyles={{
                                    table: {
                                        width: '100%',
                                        borderWidth: 1,
                                        borderColor: '#dddddd',
                                    },
                                    thead: {
                                        width: '100%',
                                        backgroundColor: '#f2f2f2'
                                    },
                                    tr: {
                                        flexDirection: 'row',
                                        width: '100%',
                                    },
                                    th: {
                                        borderWidth: 1,
                                        borderColor: '#dddddd',
                                        padding: 8,
                                        backgroundColor: '#f2f2f2',
                                        width: 150,
                                        color: color.BLACK,
                                    },
                                    td: {
                                        borderWidth: 1,
                                        borderColor: '#dddddd',
                                        padding: 8,
                                        width: 290,
                                        color: color.BLACK,
                                    },
                                    ul: { color: color.BLACK },
                                    li: { color: color.BLACK },
                                    p: { color: color.BLACK },
                                    strong: { color: color.BLACK },
                                }}
                            />
                        </ScrollView>
                    </ScrollView>
                </View>
            </View>
        </Modal>
    );
};
export default HTMLViewIDSR;

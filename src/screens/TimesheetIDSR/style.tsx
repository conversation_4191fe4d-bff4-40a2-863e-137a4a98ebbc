import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        paddingHorizontal: 14
    },
    searchView: {
        paddingTop: 16,
        paddingBottom: 16,
        gap: 10
    },
    dropDown: {
        marginTop: 5,
        borderWidth: 1,
        borderColor: color.BLACK,
        justifyContent: 'center',
        paddingLeft: 10,
        paddingRight: 10,
        borderRadius: 5,
        height: 40,
        backgroundColor: color.WHITE
    },
    parentCard: {
        margin: 10,
        alignSelf: 'center',
        flexDirection: 'row',
        borderRadius: 10,
        elevation: 5
    },
    dateBlock: {
        width: '20%',
        backgroundColor: color.ACCENT_BLUE,
        alignItems: 'center',
        justifyContent: 'center',
        rowGap: -5,
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10
    },
    titleBlock: {
        width: '75%',
        padding: 10,
        backgroundColor: color.WHITE,
        justifyContent: 'center',
        paddingLeft: 10,
        borderTopRightRadius: 10,
        borderBottomRightRadius: 10
    },
    dayText: {
        fontSize: 22,
        color: color.WHITE,
        fontWeight: '500'
    },
    monthText: {
        fontSize: 18,
        color: color.WHITE,
        fontWeight: '500'
    },
    titleText: {
        fontSize: 20,
        color: color.ACCENT_BLUE,
        fontWeight: '800'
    },
    noDataView: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    noDataText: {
        fontSize: 15,
        fontWeight: '400',
        color: color.BLACK,
        flexWrap: 'wrap'
    },
    weekDayText: {
        fontSize: 16,
        color: color.ACCENT_BLUE,
        fontWeight: '400'
    },
    container: {
        flex: 1,
        justifyContent: 'center',
        backgroundColor: color.BACKGROUND_COLOR,
        paddingVertical: 8
    },
    cardView: {
        width: '100%',
        backgroundColor: color.WHITE,
        borderRadius: 8,
        borderWidth: 2,
        borderColor: color.ACCENT_BLUE
    },
    cardUpperView: {
        flexDirection: 'row',
        backgroundColor: color.ACCENT_BLUE,
        width: '100%',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 15,
        paddingVertical: 6
    },
    projectName: {
        color: color.WHITE,
        fontSize: 17,
        fontWeight: '500',
        width: '60%',
        paddingBottom:4,
        textAlign: 'left'
    },
    dateField: {
        color: color.WHITE,
        fontSize: 16,
        fontWeight: '500',
        width: '40%',
        textAlign: 'right',
        paddingBottom:4,
        // backgroundColor:'red'
    },
    cardLowerView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 15,
        paddingVertical: 6
    },
    lowerRightView: {
        marginRight: 10
    },
    subTimeHeading: {
        fontSize: 13,
        fontWeight: '400',
        color: color.BLACK
    },
    idsrSubject: {
        color: color.BLACK,
        fontSize: 15,
        fontWeight: '500'
    },

    timesheetParentView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.6)'
    },
    timesheetInnerView : {
        maxHeight: "90%",
        width: '96%',
        alignSelf: 'center',
        marginVertical: '5%',
        paddingVertical: 20,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: color.WHITE,
        borderWidth: 2,
        borderColor: color.ACCENT_BLUE,
        borderRadius: 10,
        paddingTop: 12,
        paddingHorizontal: 10,
        paddingBottom: 20
    },
    crossBtn: {
    },
    scrollContainer:{
        width:'100%',
        flexGrow:1
    },
    subjectConatiner:{
        width:'100%',
        marginBottom: 6,
        flexDirection: 'row',
        justifyContent: 'space-between',

    },
    subjectTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: color.BLACK
    },
});
export default styles;

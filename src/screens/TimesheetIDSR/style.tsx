import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

const styles = StyleSheet.create({
    mainContainer: {
        flex: 1,
        paddingHorizontal: 14
    },
    searchView: {
        paddingTop: 16,
        paddingBottom: 16,
        gap: 10
    },
    dropDown: {
        marginTop: 5,
        borderWidth: 1,
        borderColor: color.BLACK,
        justifyContent: 'center',
        paddingLeft: 10,
        paddingRight: 10,
        borderRadius: 5,
        height: 40,
        backgroundColor: color.WHITE
    },
    noDataView: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    noDataText: {
        fontSize: 15,
        fontWeight: '400',
        color: color.BLACK,
        flexWrap: 'wrap'
    },
    
    timesheetCard: {
        backgroundColor: color.WHITE,
        borderRadius: 10,
        padding: 16,
        marginBottom: 12,
        marginHorizontal: 4,
        shadowColor: '#000',
        shadowOpacity: 0.1,
        shadowOffset: { width: 0, height: 1 },
        shadowRadius: 4,
        elevation: 3,
        borderWidth: 1,
        borderColor: '#e0e0e0',
    },
    timesheetCardHeader: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        marginBottom: 6,
    },
    timesheetHeaderDivider: {
        height: 1,
        backgroundColor: '#e0e0e0',
        marginVertical: 8,
    },
    timesheetCardDate: {
        fontWeight: 'bold',
        fontSize: 16,
        color: color.BLACK,
    },
    timesheetCardDay: {
        fontSize: 14,
        color: '#666',
    },
    timesheetStatus: {
        fontSize: 14,
        marginBottom: 6,
        color: color.BLACK,
    },
    timesheetDetail: {
        fontSize: 12,
        color: '#333',
        fontWeight: '400',
    },
    timesheetIconRow: {
        flexDirection: 'row',
        alignItems: 'center',
        gap: 20,
        marginTop: 12,
    },

    timesheetTableRow: {
        flexDirection: 'row',
        marginBottom: 8,
        alignItems: 'center',
    },
    timesheetTableHeader: {
        width: '35%',
        paddingRight: 8,
    },
    timesheetTableHeaderText: {
        fontSize: 12,
        fontWeight: '600',
        color: color.ACCENT_BLUE,
    },
    timesheetTableContent: {
        flex: 1,
    },

    
    parentCard: {
        margin: 10,
        alignSelf: 'center',
        flexDirection: 'row',
        borderRadius: 10,
        elevation: 5
    },
    dateBlock: {
        width: '20%',
        backgroundColor: color.ACCENT_BLUE,
        alignItems: 'center',
        justifyContent: 'center',
        rowGap: -5,
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10
    },
    titleBlock: {
        width: '75%',
        padding: 10,
        backgroundColor: color.WHITE,
        justifyContent: 'center',
        paddingLeft: 10,
        borderTopRightRadius: 10,
        borderBottomRightRadius: 10
    },
    dayText: {
        fontSize: 22,
        color: color.WHITE,
        fontWeight: '500'
    },
    monthText: {
        fontSize: 18,
        color: color.WHITE,
        fontWeight: '500'
    },
    titleText: {
        fontSize: 20,
        color: color.ACCENT_BLUE,
        fontWeight: '800'
    },
    weekDayText: {
        fontSize: 16,
        color: color.ACCENT_BLUE,
        fontWeight: '400'
    },
    container: {
        flex: 1,
        justifyContent: 'center',
        backgroundColor: color.BACKGROUND_COLOR,
        paddingVertical: 8
    },
    cardView: {
        width: '100%',
        backgroundColor: color.WHITE,
        borderRadius: 8,
        borderWidth: 2,
        borderColor: color.ACCENT_BLUE
    },
    cardUpperView: {
        flexDirection: 'row',
        backgroundColor: color.ACCENT_BLUE,
        width: '100%',
        justifyContent: 'space-between',
        alignItems: 'center',
        paddingHorizontal: 15,
        paddingVertical: 6
    },
    projectName: {
        color: color.WHITE,
        fontSize: 17,
        fontWeight: '500',
        width: '60%',
        paddingBottom:4,
        textAlign: 'left'
    },
    dateField: {
        color: color.WHITE,
        fontSize: 16,
        fontWeight: '500',
        width: '40%',
        textAlign: 'right',
        paddingBottom:4,
        // backgroundColor:'red'
    },
    cardLowerView: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        paddingHorizontal: 15,
        paddingVertical: 6
    },
    lowerRightView: {
        marginRight: 10
    },
    subTimeHeading: {
        fontSize: 13,
        fontWeight: '400',
        color: color.BLACK
    },
    idsrSubject: {
        color: color.BLACK,
        fontSize: 15,
        fontWeight: '500'
    },

    timesheetParentView: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.6)'
    },
    timesheetInnerView : {
        maxHeight: "90%",
        width: '96%',
        alignSelf: 'center',
        marginVertical: '5%',
        paddingVertical: 20,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: color.WHITE,
        borderWidth: 2,
        borderColor: color.ACCENT_BLUE,
        borderRadius: 10,
        paddingTop: 12,
        paddingHorizontal: 10,
        paddingBottom: 20
    },
    crossBtn: {

    },
    scrollContainer:{
        width:'100%',
        flexGrow:1
    },
    subjectConatiner:{
        width:'100%',
        marginBottom: 6,
        flexDirection: 'row',
        justifyContent: 'space-between',

    },
    subjectTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: color.BLACK
    },

    modalOverlay: {
        flex: 1,
        backgroundColor: 'rgba(0, 0, 0, 0.5)',
        justifyContent: 'center',
        alignItems: 'center',
    },
    modalContainer: {
        backgroundColor: 'white',
        borderRadius: 10,
        width: '95%',
        maxHeight: '80%',
        shadowColor: '#000',
        shadowOffset: {
            width: 0,
            height: 2,
        },
        shadowOpacity: 0.25,
        shadowRadius: 3.84,
        elevation: 5,
    },
    modalHeader: {
        padding: 15,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'flex-start',
    },
    modalTitleContainer: {
        flex: 1,
    },
    modalTitle: {
        fontSize: 16,
        fontWeight: 'bold',
        color: '#333',
        marginBottom: 5,
    },
    modalSubtitle: {
        fontSize: 12,
        color: '#666',
    },
    modalCloseButton: {
        padding: 5,
    },
    modalTableContainer: {
        padding: 10,
    },
    modalTableHeader: {
        flexDirection: 'row',
        backgroundColor: '#f5f5f5',
        paddingVertical: 8,
        paddingHorizontal: 5,
        borderBottomWidth: 1,
        borderBottomColor: '#e0e0e0',
    },
    modalTableHeaderText: {
        fontSize: 10,
        fontWeight: 'bold',
        color: '#333',
        flex: 1,
        textAlign: 'center',
        paddingHorizontal: 2,
    },
    modalTableRow: {
        flexDirection: 'row',
        paddingVertical: 8,
        paddingHorizontal: 5,
        borderBottomWidth: 1,
        borderBottomColor: '#f0f0f0',
        backgroundColor: 'white',
    },
    modalTableCell: {
        fontSize: 10,
        color: '#333',
        flex: 1,
        textAlign: 'center',
        paddingHorizontal: 2,
    },
    modalSubmissionInfo: {
        padding: 15,
        borderTopWidth: 1,
        borderTopColor: '#e0e0e0',
        backgroundColor: '#f9f9f9',
    },
    modalSubmissionText: {
        fontSize: 12,
        color: '#666',
        textAlign: 'center',
    },
});
export default styles;

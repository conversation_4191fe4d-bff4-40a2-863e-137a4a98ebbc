import React, { useEffect, useState } from 'react';
import {
  View,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  ScrollView,
  Alert,
  SafeAreaView,
  BackHandler,
  Text
} from 'react-native';
import RNText from '../../component/RNText';
import MaterialTextField from '../../component/MaterialTextField';
import { httpGet, httpPatch } from '../../utils/http';
import apiConstant from '../../utils/constants/apiConstant';
import RNActivityIndicator from '../../component/Loader';
import { Dropdown } from 'react-native-element-dropdown';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { useNavigation, useRoute } from '@react-navigation/native';
import { navigationStringText } from '../../utils/constants/navigationStringText';

type EditTimesheetRouteParams = {
  timesheetId: number;
};

const EditTimesheetScreen = () => {
  const navigation = useNavigation<any>();
  const route = useRoute();
  const timesheetId = (route.params as EditTimesheetRouteParams)?.timesheetId;

  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<any[]>([]);
  const [toEmail, setToEmail] = useState('<EMAIL>');
  const [ccEmail, setCcEmail] = useState('');
  const [emailError, setEmailError] = useState('');
  const [projectOptions, setProjectOptions] = useState<{ label: string; value: string }[]>([]);
  const [userId, setUserId] = useState<number | null>(null);
  const [rawEditData, setRawEditData] = useState<any[]>([]);

  const [priorityOptions] = useState([
    { label: 'High', value: 'High' },
    { label: 'Medium', value: 'Medium' },
    { label: 'Low', value: 'Low' }
  ]);

  const [statusOptions, setStatusOptions] = useState<{ label: string; value: string; id?: number }[]>([]);
  const [mandateOptions, setMandateOptions] = useState<{ label: string; value: string; id?: number }[]>([]);

  useEffect(() => {
    if (timesheetId) {
      setLoading(true);
      fetchUserId();
      fetchStatusOptions();
      fetchMandateOptions();
      fetchManagerEmail();
    }
  }, [timesheetId]);

  useEffect(() => {
    const backAction = () => {
      navigation.navigate(navigationStringText.WorkInfo, {
        preserveTabIndex: true,
        refreshData: true,
        isFromEditTimesheet: true
      });
      return true;
    };

    const backHandler = BackHandler.addEventListener(
      'hardwareBackPress',
      backAction
    );

    return () => backHandler.remove();
  }, [navigation]);


  useEffect(() => {
    if (timesheetId) {
      fetchEditDetails();
    }
  }, [timesheetId]);

  useEffect(() => {
    if (userId) {
      fetchProjectOptions();
    }
  }, [userId]);

  useEffect(() => {
    if (rawEditData.length > 0 && projectOptions.length > 0 && mandateOptions.length > 0) {
      processFormData();
    }
  }, [rawEditData, projectOptions, mandateOptions]);

  const fetchManagerEmail = async () => {
    try {
      const response = await httpGet(apiConstant.DRs_LIST);
      const drsData = JSON.parse(response)?.data;
      if (drsData?.managerDetails?.email) {
        setToEmail(drsData.managerDetails.email);
      } else {
        setToEmail('<EMAIL>');
      }
    } catch (error) {
      console.log('Error fetching manager email:', error);
      setToEmail('<EMAIL>');
    }
  };


  const processFormData = () => {
    const formatted = rawEditData.map((item: any) => {
      let statusName = statusOptions.find(s => s.value === item.status) || null;
      if (!statusName) {
        if (item.statusId === 2) statusName = { label: 'In Progress', value: 'In Progress', id: 2 };
        else statusName = { label: 'Completed', value: 'Completed', id: 5 };
      }

      let projectName = item.projectName || '';
      let otherProjectName = item.otherProjectName || '';

      const projectExists = projectOptions.find(p => p.value === projectName);
      if (projectName && !projectExists) {
        otherProjectName = projectName;
        projectName = 'Other';
      }

      let type = item.type || '';
      const mandateId = item.mandateId;
      const typeExists = mandateOptions.find(m => m.value === type);
      if (type && !typeExists) {
        type = mandateOptions.length > 0 ? mandateOptions[0].value : '';
      }

      if (mandateId) {
        const mandateOption = mandateOptions.find(m => m.id === mandateId);
        if (mandateOption) {
          type = mandateOption.value;

        } else {
          type = mandateOptions.length > 0 ? mandateOptions[0].value : '';
        }
      } else {
        type = mandateOptions.length > 0 ? mandateOptions[0].value : '';
      }

      return {
        ...item,
        status: statusName.value,
        statusId: statusName.id,
        description: stripHtmlTags(item.taskDescription || ''),
        projectName: projectName,
        otherProjectName: otherProjectName,
        type: type
      };
    });

    setFormData(formatted);
  };

  const fetchUserId = async () => {
    try {
      const response = await httpGet(apiConstant.USER_INFO);
      const userData = JSON.parse(response)?.data;
      if (userData) {
        setUserId(userData.id);
      }
    } catch (error) {
      console.log('Error fetching user info:', error);
    }
  };

  const fetchProjectOptions = async () => {
    if (!userId) return;

    try {
      const response = await httpGet(`${apiConstant.PROJECTS}?userId=${userId}`);
      const parsed = JSON.parse(response);
      console.log('Project API response:', parsed);

      if (parsed?.data && Array.isArray(parsed.data)) {
        const options = [
          ...parsed.data.map((project: string) => ({ label: project, value: project })),
          { label: 'Other', value: 'Other' }
        ];
        setProjectOptions(options);
      } else {
        setProjectOptions([{ label: 'Other', value: 'Other' }]);
      }
    } catch (error) {
      console.log('Error fetching projects:', error);
      setProjectOptions([{ label: 'Other', value: 'Other' }]);
    }
  };

  const fetchStatusOptions = async () => {
    try {
      const parsed = await httpGet(`${apiConstant.STATUS}`);
      const response = JSON.parse(parsed);
      if (response && Array.isArray(response.data)) {
        const options = response.data.map((item: any) => ({
          label: item.status_name,
          value: item.status_name,
          id: item.id
        }));
        setStatusOptions(options);
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to fetch status options.');
    }
  };

  const fetchMandateOptions = async () => {
    try {
      const parsed = await httpGet(`${apiConstant.MANDATE_TYPES}`);
      const response = JSON.parse(parsed);
      if (response && response.data && Array.isArray(response.data.data)) {
        const options = response.data.data.map((item: any) => ({
          label: item.mandate_name,
          value: item.mandate_name,
          id: item.id
        }));
        setMandateOptions(options);
      } else {
        console.log("No valid mandate data found in response");
      }
    } catch (error) {
      Alert.alert('Error', 'Failed to fetch mandate options.');
    }
  };

  const fetchEditDetails = async () => {
    try {
      const response = await httpGet(`${apiConstant.EDIT_TIMESHEET_DETAILS}?idsrId=${timesheetId}`);
      const parsed = JSON.parse(response);
      if (parsed?.data?.length > 0) {
        setRawEditData(parsed.data);
        const formatted = parsed.data.map((item: any) => {
          let statusName = statusOptions.find(s => s.value === item.status) || null;
          if (!statusName) {
            if (item.statusId === 2) statusName = { label: 'In Progress', value: 'In Progress', id: 2 };
            else statusName = { label: 'Completed', value: 'Completed', id: 5 };
          }
          return {
            ...item,
            status: statusName.value,
            statusId: statusName.id,
            description: stripHtmlTags(item.taskDescription || ''),
            type: item.type || 'Default'
          };
        });
        setFormData(formatted);
      }
    } catch (e) {
      Alert.alert("Error", "Failed to load timesheet details.");
    } finally {
      setLoading(false);
    }
  };

  const handleAddProject = () => {
    const newProject = {
      projectName: '',
      otherProjectName: '',
      timeSpend: '',
      description: '',
      priority: 'Medium',
      status: 'In Progress',
      statusId: 2,
      type: '',
      mandateId: '0',
      no_of_pr_raised: 0,
      no_of_pr_approved: 0,
      no_of_reworked_pr: 0
    };
    setFormData([...formData, newProject]);
  };

  const handleRemoveProject = (index: number) => {
    if (formData.length === 1) return;
    const updated = formData.filter((_, i) => i !== index);
    setFormData(updated);
  };

  const updateField = (index: number, key: string, value: any) => {
    const updated = [...formData];
    if (key === 'timeSpend') {
      const timeValue = parseInt(value) || 0;
      if (timeValue > 240) {
        return;
      }
    }
    if (key === 'projectName') {
      if (value !== 'Other') {
        updated[index]['otherProjectName'] = '';
      }
    }
    updated[index][key] = value;
    setFormData(updated);
  };

  const stripHtmlTags = (html: string) => {
    if (!html) return '';
    return html.replace(/<[^>]*>?/gm, '');
  };

  const validateEmail = (email: string) => {
    const emailArray = email.split(',').map(e => e.trim());
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    const invalidEmails = emailArray.filter(e => !emailRegex.test(e));
    if (invalidEmails.length > 0) {
      setEmailError(`Invalid email(s): ${invalidEmails.join(', ')}`);
      return false;
    }
    setEmailError('');
    return true;
  };

  const handleSubmit = async () => {
    if (!validateEmail(toEmail)) return;


    const responseBody = {
      to: toEmail,
      cc: ccEmail,
      idsrData: formData.map(item => ({
        projectName: item.projectName || 'Unknown',
        otherProjectName: item.otherProjectName || '',
        timeSpend: item.timeSpend?.toString() || '',
        taskDescription: `<p>${item.description || ''}</p>`,
        priority: item.priority || 'Medium',
        statusId: String(statusOptions.find(s => s.value === item.status)?.id || item.statusId || 2),
        mandateId: item.mandateId || '0',
        no_of_pr_raised: item.no_of_pr_raised || 0,
        no_of_pr_approved: item.no_of_pr_approved || 0,
        no_of_reworked_pr: item.no_of_reworked_pr || 0
      })),
      is_draft: 0
    };

    try {
      console.log('Submitting PATCH request:', JSON.stringify(responseBody, null, 2));
      console.log("URL Is", `${apiConstant.SUBMIT_IDSR}?idsrId=${timesheetId}`);
      await httpPatch(`${apiConstant.SUBMIT_IDSR}?idsrId=${timesheetId}`, responseBody);
      navigation.navigate(navigationStringText.WorkInfo, {
        refreshData: true,
        timestamp: Date.now()
      });
    } catch (error: any) {
      Alert.alert('Error', 'Failed to update timesheet: ' + error.message);
    }
  };

  const isFormValid = () => {
    if (!toEmail?.trim()) {
      return false;
    }

    let hasValidTask = false;
    for (let i = 0; i < formData.length; i++) {
      const item = formData[i];

      const isProjectValid = item.projectName && item.projectName.trim() !== '';
      const isTypeValid = item.type && item.type.trim() !== '';
      const isStatusValid = item.status && item.status.trim() !== '';
      const isPriorityValid = item.priority && item.priority.trim() !== '';
      const isTimeSpentValid = item.timeSpend && item.timeSpend.toString().trim() !== '';
      const isDescriptionValid = item.description && item.description.trim() !== '';

      const isOtherProjectValid = item.projectName === 'Other' ?
        (item.otherProjectName && item.otherProjectName.trim() !== '') : true;

      if (isProjectValid && isTypeValid && isStatusValid && isPriorityValid &&
        isTimeSpentValid && isDescriptionValid && isOtherProjectValid) {
        hasValidTask = true;
        break;
      }
    }

    return hasValidTask;
  };

  return (
    <SafeAreaView style={styles.container}>

      <ScrollView contentContainerStyle={{ paddingVertical: 30 }} showsVerticalScrollIndicator={false}>
        {loading ? (
          <RNActivityIndicator animating={true} />
        ) : formData.length > 0 ? (
          <>
            <View style={styles.field}>
              <MaterialTextField
                label={
                  <Text>
                    <Text>To </Text>
                    <Text style={styles.asterisk}>*</Text>
                  </Text>
                }
                value={toEmail}
                onChangeText={setToEmail}
                keyboardType="email-address"
                style={[styles.materialTextField, emailError ? styles.inputError : {}]}

              />
              {emailError ? <RNText style={styles.errorText}>{emailError}</RNText> : null}
            </View>

            <View style={styles.field}>
              <MaterialTextField
                label="Cc"
                value={ccEmail}
                onChangeText={setCcEmail}
                keyboardType="email-address"
                style={styles.materialTextField}
              />
            </View>

            {formData.map((item, index) => (
              <View key={index} style={{ marginTop: 10 }}>
                <View style={styles.row}>
                  <View style={styles.half}>
                    <View style={styles.labelContainer}>
                      <RNText style={styles.label}>Select Project </RNText>
                      <RNText style={styles.asterisk}>*</RNText>
                    </View>
                    <Dropdown
                      placeholder="Select Project"
                      data={projectOptions}
                      value={item.projectName}
                      labelField="label"
                      valueField="value"
                      style={styles.dropdown}
                      placeholderStyle={styles.dropdownText}
                      selectedTextStyle={styles.dropdownText}
                      onChange={(val) => updateField(index, 'projectName', val.value)}
                    />
                  </View>

                  {item.projectName === 'Other' && (
                    <View style={styles.half}>
                      <View style={styles.labelContainer}>
                        <RNText style={styles.label}>Project Name </RNText>
                        <RNText style={styles.asterisk}>*</RNText>
                      </View>
                      <TextInput
                        placeholder="Enter project name"
                        value={item.otherProjectName}
                        onChangeText={(val) => updateField(index, 'otherProjectName', val)}
                        style={styles.input}
                      />
                    </View>
                  )}
                </View>

                <View style={styles.row}>
                  <View style={styles.half}>
                    <View style={styles.labelContainer}>
                      <RNText style={styles.label}>Select Type </RNText>
                      <RNText style={styles.asterisk}>*</RNText>
                    </View>
                    <Dropdown
                      placeholder="Select Type"
                      data={mandateOptions}
                      value={item.type}
                      labelField="label"
                      valueField="value"
                      style={styles.dropdown}
                      placeholderStyle={styles.dropdownText}
                      selectedTextStyle={styles.dropdownText}
                      selectedTextProps={{ numberOfLines: 1, ellipsizeMode: 'tail' }} 
                      onChange={(val) => updateField(index, 'type', val.value)}
                    />
                  </View>

                  <View style={styles.half}>
                    <View style={styles.labelContainer}>
                      <RNText style={styles.label}>Task Status </RNText>
                      <RNText style={styles.asterisk}>*</RNText>
                    </View>
                    <Dropdown
                      placeholder={item.status}
                      data={statusOptions}
                      value={item.status}
                      labelField="label"
                      valueField="value"
                      style={styles.dropdown}
                      placeholderStyle={styles.dropdownText}
                      selectedTextStyle={styles.dropdownText}
                      onChange={(val) => updateField(index, 'status', val.value)}
                    />
                  </View>
                </View>

                <View style={styles.row}>
                  <View style={styles.half}>
                    <View style={styles.labelContainer}>
                      <RNText style={styles.label}>Priority </RNText>
                      <RNText style={styles.asterisk}>*</RNText>
                    </View>
                    <Dropdown
                      placeholder={item.priority}
                      data={priorityOptions}
                      value={item.priority}
                      labelField="label"
                      valueField="value"
                      style={styles.dropdown}
                      placeholderStyle={styles.dropdownText}
                      selectedTextStyle={styles.dropdownText}
                      onChange={(val) => updateField(index, 'priority', val.value)}
                    />
                  </View>

                  <View style={styles.half}>
                    <View style={styles.labelContainer}>
                      <RNText style={styles.label}>Time Spent(Minutes) </RNText>
                      <RNText style={styles.asterisk}>*</RNText>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                      <TextInput
                        value={item.timeSpend?.toString()}
                        onChangeText={(val) => updateField(index, 'timeSpend', val)}
                        style={[styles.input, { flex: 1 }]}
                        keyboardType="numeric"
                        placeholder="Max. 240 minutes"
                        maxLength={3}
                      />
                      {index === formData.length - 1 && (
                        <TouchableOpacity onPress={handleAddProject} style={{ marginLeft: 4 }}>
                          <Ionicons name="add-circle" size={28} color="#388E3C" />
                        </TouchableOpacity>
                      )}
                      {formData.length > 1 && (
                        <TouchableOpacity onPress={() => handleRemoveProject(index)} style={{ marginLeft: 2 }}>
                          <Ionicons name="close-circle" size={28} color="#D32F2F" />
                        </TouchableOpacity>
                      )}
                    </View>
                  </View>
                </View>

                <View style={styles.fullWidth}>
                  <View style={styles.labelContainer}>
                    <RNText style={styles.label}>Description </RNText>
                    <RNText style={styles.asterisk}>*</RNText>
                  </View>
                  <TextInput
                    placeholder="Enter description"
                    value={item.description}
                    onChangeText={(val) => updateField(index, 'description', val)}
                    style={styles.descriptionInput}
                    multiline
                  />
                </View>

                {index !== formData.length - 1 && <View style={styles.divider} />}
              </View>
            ))}

            <View style={styles.buttonRow}>
              <TouchableOpacity onPress={() => 
               navigation.navigate(navigationStringText.WorkInfo, {
                refreshData: true,
                timestamp: Date.now(),
                isFromEditTimesheet: true
              })
                } style={styles.cancelButton}>
                <RNText style={styles.cancelText}>CANCEL</RNText>
              </TouchableOpacity>
              <TouchableOpacity
                style={[
                  styles.submitButton,
                  (!isFormValid() || loading) && styles.submitButtonDisabled
                ]}
                onPress={handleSubmit}
                disabled={!isFormValid() || loading}
              >
                <RNText style={[
                  styles.submitText,
                  (!isFormValid() || loading) && styles.submitTextDisabled
                ]}>
                  {loading ? 'Submitting...' : 'SUBMIT'}
                </RNText>
              </TouchableOpacity>
            </View>
          </>
        ) : (
          <RNText>No data available</RNText>
        )}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    backgroundColor: 'white',
    borderBottomWidth: 1,
    borderBottomColor: '#e0e0e0',
    marginBottom: 20,
  },
  backButton: {
    marginRight: 15,
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    textAlign: 'center',
    flex: 1,
  },
  field: {
    paddingHorizontal: 20,
  },
  materialTextField: {
    marginBottom: 16,
  },
  label: {
    fontWeight: '600',
    marginBottom: 5,
    color: '#333',
  },
  input: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    paddingHorizontal: 10,
    paddingVertical: 8,
    backgroundColor: '#f9f9f9',
    color: '#333',
    height: 40,
    fontSize: 14,
  },
  descriptionInput: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    paddingHorizontal: 10,
    paddingVertical: 8,
    backgroundColor: '#f9f9f9',
    color: '#333',
    height: 140,
    fontSize: 14,
    textAlignVertical: 'top',
  },
  inputError: {
    borderColor: 'red',
  },
  errorText: {
    color: 'red',
    marginTop: 4,
    fontSize: 12,
  },
  dropdown: {
    borderWidth: 1,
    borderColor: '#ccc',
    borderRadius: 6,
    paddingHorizontal: 10,
    backgroundColor: '#f9f9f9',
    marginBottom: 10,
    height: 48,
    justifyContent: 'center',
  },
  dropdownText: {
    color: '#333',
    fontSize: 14,
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    gap: 10,
    marginBottom: 15,
    paddingHorizontal: 20,
  },
  fullWidth: {
    marginBottom: 15,
    paddingHorizontal: 20,
  },
  half: {
    flex: 1,
  },
  divider: {
    height: 1,
    backgroundColor: '#ccc',
    marginVertical: 10,
    marginHorizontal: 20,
  },
  buttonRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 20,
    paddingHorizontal: 20,
  },
  cancelButton: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#1E3A8A',
    borderRadius: 8,
    paddingVertical: 10,
    marginRight: 10,
    alignItems: 'center',
  },
  submitButton: {
    flex: 1,
    backgroundColor: '#1E3A8A',
    borderRadius: 8,
    paddingVertical: 10,
    alignItems: 'center',
  },
  cancelText: {
    color: '#1E3A8A',
    fontWeight: '600',
  },
  submitText: {
    color: 'white',
    fontWeight: '600',
  },
  labelContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 5,
  },
  asterisk: {
    color: '#FF0000',
    fontWeight: 'bold',
  },
  submitButtonDisabled: {
    backgroundColor: '#1E3A8A',
    opacity: 0.5,
  },
  submitTextDisabled: {
    color: 'white',
    opacity: 0.7,
  },
  
});

export default EditTimesheetScreen;
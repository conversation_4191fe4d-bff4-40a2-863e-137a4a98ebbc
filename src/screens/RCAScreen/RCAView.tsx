import React from 'react';
import RenderHTML from 'react-native-render-html';
import {
    View,
    Modal,
    useWindowDimensions,
    ScrollView
} from 'react-native';
import { color } from '../../utils/constants/color';
import styles from './styles';
import Ionicons from 'react-native-vector-icons/Ionicons';
import RNButton from '../../component/RNButton';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';

interface Props {
    isVisible: boolean;
    onConfirm: () => void;
    body: string;
    subject: string;
}

const HTMLViewRCA: React.FC<Props> = ({
    body,
    isVisible,
    subject,
    onConfirm
}) => {
    const toggleModal = () => {
        onConfirm();
    };
    const { width } = useWindowDimensions();
    const htmlHeading = `<div><h3>${subject}</h3></div>`;
    const modifiedHTML = body;
    return (
        <Modal transparent={true} visible={isVisible}>
            <View style={styles.rcaViewMainContainer}>
                <View style={styles.rcaViewInnerContainer}>
                    <ScrollView style={styles.scrollContainer}>
                        <RNButton
                            handleOnPress={toggleModal}
                            style={styles.crossBtn}
                        >
                            <Ionicons name="close-outline" size={28} color="black" />
                        </RNButton>
                        <View style={styles.subjectConatiner}>
                            <RenderHTML
                                contentWidth={width}
                                source={{ html: htmlHeading }}
                                tagsStyles={{
                                    h3: {
                                        color: color.BLACK,
                                        textAlign: 'center'
                                    },
                                    p: { color: color.BLACK },
                                    ul: { color: color.BLACK },
                                    body: { padding: 10, width: '96%' },
                                    td: { color: color.BLACK },
                                    strong: { color: color.BLACK },
                                    li: { color: color.BLACK },
                                }}
                            />
                        </View>

                        <RenderHTML
                            contentWidth={width}
                            source={{ html: modifiedHTML }}
                            tagsStyles={{
                                h3: {
                                    color: color.BLACK,
                                    textAlign: 'center'
                                },
                                p: { color: color.BLACK },
                                ul: { color: color.BLACK },
                                body: { padding: 10, width: '96%' },
                                td: { color: color.BLACK },
                                strong: { color: color.BLACK },
                                li: { color: color.BLACK },
                            }}
                        />
                    </ScrollView>
                </View>
            </View>
        </Modal>
    );
};

export default HTMLViewRCA;

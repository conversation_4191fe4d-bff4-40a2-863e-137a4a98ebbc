import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

const styles = StyleSheet.create({
    searchView: {
        paddingTop: 16,
        paddingBottom: 16,
        gap: 10
    },
    dropDown: {
        marginTop: 5,
        borderWidth: 1,
        borderColor: color.BLACK,
        justifyContent: 'center',
        paddingLeft: 10,
        paddingRight: 10,
        borderRadius: 5,
        height: 40,
        backgroundColor: color.WHITE
    },
    parentCard: {
        margin: 10,
        alignSelf: 'center',
        flexDirection: 'row',
        borderRadius: 10,
        elevation: 5
    },
    dateBlock: {
        width: '20%',
        backgroundColor: color.ACCENT_BLUE,
        alignItems: 'center',
        justifyContent: 'center',
        rowGap: -5,
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10
    },
    titleBlock: {
        width: '75%',
        padding: 10,
        backgroundColor: color.WHITE,
        justifyContent: 'center',
        paddingLeft: 10,
        borderTopRightRadius: 10,
        borderBottomRightRadius: 10
    },
    dayText: {
        fontSize: 22,
        color: color.WHITE,
        fontWeight: '500'
    },
    monthText: {
        fontSize: 18,
        color: color.WHITE,
        fontWeight: '500'
    },
    titleText: {
        fontSize: 20,
        color: color.ACCENT_BLUE,
        fontWeight: '800'
    },
    weekDayText: {
        fontSize: 16,
        color: color.ACCENT_BLUE,
        fontWeight: '400'
    },
    container: {
        flex: 1,
        justifyContent: 'center',
        paddingVertical: 8,
        paddingHorizontal: 10
    },
    cardView: {
        width: '100%',
        backgroundColor: color.WHITE,
        borderRadius: 5,
        borderWidth: 1,
        borderColor: color.ACCENT_BLUE
    },
    cardUpperView: {
        flexDirection: 'row',
        backgroundColor: color.ACCENT_BLUE,
        width: '100%',
        justifyContent: 'space-between',
        paddingHorizontal: 15,
        paddingVertical: 6
    },
    projectName: {
        color: color.WHITE,
        fontSize: 17,
        fontWeight: '500',
        width: '50%',
        textAlign: 'left'
    },
    dateField: {
        color: color.WHITE,
        fontSize: 16,
        fontWeight: '500',
        width: '50%',
        textAlign: 'right',
    },
    cardLowerView: {
        flexDirection: 'row',
        width: '100%',
        justifyContent: 'space-between',
        paddingHorizontal: 15,
        paddingVertical: 6
    },
    lowerRightView: {
        marginRight: 10
    },
    subTimeHeading: {
        fontSize: 13,
        fontWeight: '400',
        color: color.BLACK
    },
    textLabel: {
        color: color.BLACK,
        fontSize: 15,
        fontWeight: '500',
        width: "100%",
    },
    noDataView: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    noDataText: {
        fontSize: 15,
        fontWeight: '400',
        color: color.BLACK,
        flexWrap: 'wrap'
    },
    rcaViewMainContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.6)'
    },
    rcaViewInnerContainer: {
        height: '90%',
        width: '85%',
        alignSelf: 'center',
        marginVertical: '5%',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: color.WHITE,
        borderWidth: 2,
        borderColor: color.ACCENT_BLUE,
        borderRadius: 10,
        paddingTop: 4,
        // paddingHorizontal: 6,
        paddingBottom: 8
    },
    crossBtn: {
        // margin: 10,
        // position: 'absolute',
        // top: 4,
        // right: 16
        justifyContent: 'flex-end',
        alignSelf: 'flex-end'
    },
    scrollContainer: {
        flexGrow:1,
        width: '100%'
    },
    subjectConatiner: {
        width: '100%',
        marginBottom: -15
    }
});
export default styles;

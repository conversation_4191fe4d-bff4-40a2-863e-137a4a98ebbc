import React, { useEffect, useState } from "react";
import { FlatList, SafeAreaView, View } from "react-native";
import RNText from "../../component/RNText";
import { httpGet } from "../../utils/http";
import { stringText } from "../../utils/constants/stringsText";
import RCAListCard from "./RcaCard";
import styles from "./styles";
import RNActivityIndicator from "../../component/Loader";
import apiConstant from "../../utils/constants/apiConstant";

export type Props = {
    navigation: any;
    route: any;
};

export type rcaType = {
    created_at: string;
    subject: string;
    manager_name: string;
    project_name: string;
    body: string;
    other_project_name: string;
};

const RCAScreen: React.FC<Props> = ({
    navigation,
    route
}) => {
    const [userId, setUserId] = useState<any>(route?.params?.userId);
    const [rca, setRca] = useState<rcaType[]>([]);
    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);

    const errorHandlerClicked = (
        visibility: boolean,
        message: string
    ) => {
        setErrorHandlerVisibility(visibility);
        setErrorHandlerMessage(message);
    };

    useEffect(() => {
        if (!(route?.params?.userId)) {
            fetchUserId();
        }
        else if (userId) {
            fetchRca();
        }
    }, []);

    useEffect(() => {
        if (userId) {
            fetchRca();
        }
    }, [userId]);

    const fetchUserId = () => {
        setFetching(true);
        httpGet(apiConstant.USER_INFO)
            .then((response: any) => {
                const user = JSON.parse(response)?.data;
                if (user) {
                    setUserId(user?.id);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
            });
    };

    const fetchRca = () => {
        setFetching(true);
        httpGet(`${apiConstant.RCA}?userId=${userId}`)
            .then((response: any) => {
                const Rca = JSON.parse(response)?.data;
                if (Rca) {
                    setRca(Rca);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
            });
    };

    const renderRCAItem = ({ item }: { item: rcaType }) => (
        <RCAListCard
            created_at={item.created_at}
            subject={item.subject}
            manager_name={item.manager_name}
            project_name={item.project_name}
            body={item.body}
            other_project_name={item.other_project_name}
        />
    );

    return (
        <SafeAreaView style={{ flex: 1 }}>
            {
                rca.length > 0 && !fetching ?
                    <FlatList
                        data={rca}
                        renderItem={renderRCAItem}
                        keyExtractor={(item, index) => index.toString()}
                        refreshing={fetching}
                        onRefresh={fetchRca}
                        contentContainerStyle={{ paddingTop: 5, paddingBottom: 30 }}
                        showsVerticalScrollIndicator={false}
                    /> :
                    (!fetching &&
                        <View
                            style={styles.noDataView}
                        >
                            <RNText style={styles.noDataText}>{stringText.DataNotAvailable}</RNText>
                        </View>
                    )
            }
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>

    );
};

export default RCAScreen;

import React, { useEffect, useState } from 'react';
import { SafeAreaView, View } from 'react-native';
import RNText from '../../component/RNText';
import styles from './styles';
import HTMLViewRCA from './RCAView';
import { stringText } from '../../utils/constants/stringsText';
import RNButton from '../../component/RNButton';

const RCAListCard: React.FC<{
    created_at: string,
    subject: string,
    manager_name: string,
    project_name: string,
    body: string;
    other_project_name: string;
}> = ({ subject, created_at, manager_name, project_name, body, other_project_name }) => {
    const dateObj = new Date(created_at);
    const [projectName, setProjectName] = useState<string>(project_name)
    const formatDate = (dateString: string): string => {
        const date = new Date(dateString);
        const options:Intl.DateTimeFormatOptions = {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric',
        };
        const formattedDate = new Intl.DateTimeFormat('en-US', options).format(date);

        return formattedDate;
    };
    useEffect(() => {
        if (other_project_name) {
            setProjectName(other_project_name);
        }
    }, [project_name]);

    const [rcaPopup, setRCAPopup] = useState<boolean>(false);
    const handleRCAPopup = () => {
        setRCAPopup(!rcaPopup);
    };
    return (
        <SafeAreaView style={[styles.container]}>
            <RNButton 
            ActiveOpacity={0.6} 
            handleOnPress={handleRCAPopup}
            >
                <View style={styles.cardView}>
                    <View style={styles.cardUpperView}>
                        <RNText style={[styles.projectName]}>
                            {projectName ? projectName : "---"}
                        </RNText>
                        <RNText style={styles.dateField}>
                            {formatDate(created_at) ? formatDate(created_at) : "---"}
                        </RNText>
                    </View>
                    <View style={styles.cardLowerView}>
                        <View style={{ width:'60%'}}>
                            <RNText style={styles.textLabel}>
                                {stringText.Subject}
                            </RNText>
                            <RNText style={styles.textLabel}>
                                {subject ? subject : "---"}
                            </RNText>
                        </View>
                        <View style={{ width:'38%'}}>
                            <RNText style={styles.textLabel}>
                                {stringText.Manager}
                            </RNText>
                            <RNText style={styles.textLabel}>
                                {manager_name ? manager_name : "---"}
                            </RNText>
                        </View>
                    </View>
                </View>
            </RNButton>
            <HTMLViewRCA
                isVisible={rcaPopup}
                onConfirm={handleRCAPopup}
                body={body}
                subject={subject}
            />
        </SafeAreaView>
    );
};

export default RCAListCard;

import React, { useEffect, useState } from 'react';
import { Text, View } from 'react-native';
import moment from 'moment';
import { httpGet, httpPost } from '../../utils/http';
import HistoryView from './HistoryView';
import styles from './styles';
import { stringText } from '../../utils/constants/stringsText';
import { color } from '../../utils/constants/color';
import { OrderStatus } from '../../utils/enumConstants';
import RNImage from '../../component/RNImage';
import RNButton from '../../component/RNButton';
import imageConstant from '../../utils/constants/imageConstant';
import apiConstant from '../../utils/constants/apiConstant';

const History = () => {

    const [datesearch, setDateSearch] = useState(new Date());
    const [datelast, setDateLast] = useState(new Date());
    const [opensearch, setOpenSearch] = useState<boolean>(false);
    const [openlast, setOpenlast] = useState<boolean>(false);
    const [fetching, setFetching] = useState<boolean>(false);
    const [tableData, setTableData] = useState<any>();
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);
    const [todaydate, setTodaydate] = useState(new Date());

    useEffect(() => {
        LoadHistory();
    }, []);

    useEffect(() => {
        LoadHistory();
    }, [datesearch, datelast]);

    const LoadHistory = () => {
        if (datesearch?.valueOf() > datelast?.valueOf()) {
            errorHandlerClicked(
                true,
                `${stringText.SelectCorrectDate}`
            );
        } else {
            setFetching(true);
            httpGet(
                `${apiConstant.FOOD_HISTORY}?order_type=2&from_date=${moment(
                    datesearch
                ).format('YYYY-MM-DD')}&to_date=${moment(datelast).format(
                    'YYYY-MM-DD'
                )}`
            )
            .then((response: any) => {
                const tableColumn: Array<any> = [];
                if (JSON.parse(response)?.data?.length > 0) {
                    JSON.parse(response)?.data?.map(
                        (item: any, index: number) => {
                            const date = item.date                            
                            const innerItem = [
                                date,
                                getOrderIdViewCell(
                                    todaydate,
                                    date,
                                    item?.id,
                                    item?.status
                                ),
                                getOrderStatusViewCell(item?.status)
                            ];
                            tableColumn?.push(innerItem);
                        }
                    );
                }

                const tableData = {
                    tableHead: [`${stringText.DateText}`, `${stringText.OrderIDText}`, `${stringText.Status}`],
                    tableData: tableColumn
                };
                setTableData(tableData);
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
        }
    };

    const getOrderIdViewCell = (
        todaydate: any,
        date: any,
        id: any,
        status: string
    ) => {
        var todayDate = moment(todaydate).format('DD/MM/YYYY');

        return (
            <View
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row'
                }}
            >
                <Text style={styles.text}>{id}</Text>
                {todayDate === date && status !== OrderStatus.CANCELLED && (
                    <RNButton
                        handleOnPress={() => cancelOrderButtonFunction(id)}
                        style={{ position: 'absolute', right: 10 }}
                    >
                        <RNImage
                            source={imageConstant.CancelIcon}
                            style={{
                                height: 15,
                                width: 15,
                                resizeMode: 'stretch'
                            }}
                        />
                    </RNButton>
                )}
            </View>
        );
    };

    const getOrderStatusViewCell = (status: string) => {
        return (
            <View
                style={{
                    flex: 1,
                    justifyContent: 'center',
                    alignItems: 'center',
                    flexDirection: 'row'
                }}
            >
                <Text
                    style={[
                        styles.text,
                        status === OrderStatus.CANCELLED
                            ? { color: color.PRIMARY_ACTIVE }
                            : { color: color.DARK_GREEN }
                    ]}
                >
                    {status}
                </Text>
            </View>
        );
    };

    const cancelOrderButtonFunction = (id: number) => {
        setFetching(true);
        const payload = {
            food_order_id: id
        };
        httpPost('/cancel_food_order/', payload)
            .then(async (res: any) => {
                setFetching(false);
                const response = JSON.parse(res);
                LoadHistory();
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(
                        true,
                        err?.response?.data?.message
                    );
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    return (
        <HistoryView
            datesearch={datesearch}
            setOpenSearch={setOpenSearch}
            datelast={datelast}
            setOpenlast={setOpenlast}
            opensearch={opensearch}
            setDateSearch={setDateSearch}
            openlast={openlast}
            setDateLast={setDateLast}
            tableData={tableData}
            fetching={fetching}
            errorHandlerVisibility={errorHandlerVisibility}
            errorHandlerMessage={errorHandlerMessage}
            errorHandlerClicked={errorHandlerClicked}
        />
    );
};

export default History;

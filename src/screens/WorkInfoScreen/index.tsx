import React, { useState, useEffect } from 'react';
import { TabView, TabBar } from 'react-native-tab-view';
import PlanForTheDay from '../PlanForTheDay';
import Timesheet from '../TimesheetIDSR';
import { useNavigation, useRoute, useFocusEffect } from '@react-navigation/native';

const WorkInfoScreen = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const [index, setIndex] = useState(0);
  console.log("index", index);
  const [refreshKey, setRefreshKey] = useState(0);
  const [routes] = useState([
    { key: 'plan', title: 'Plan For The Day' },
    { key: 'timesheet', title: 'Timesheet' },
  ]);


useFocusEffect(
  React.useCallback(() => {
    const params = route?.params as any;

    setRefreshKey(prev => prev + 1);
    if (!params?.isFromEditTimesheet) {
      setIndex(0)
    }
  }, [])
);

useEffect(() => {
  const params = route?.params as any;

  if (params?.refreshData) {
    setRefreshKey(prev => prev + 1);
    navigation.setParams({ refreshData: false });
  }
}, [route?.params]);


useEffect(() => {
  const params = route?.params as any;
    if (params?.isFromEditTimesheet) {
      setTimeout(() => {
        setIndex(1);
      }, 600);
      navigation.setParams({ isFromEditTimesheet: false });
    }
}, [route?.params]);



  const renderScene = ({ route }: any) => {
    switch (route.key) {
      case 'plan':
        return (
          <PlanForTheDay
            key={`plan-${refreshKey}`}
            navigation={navigation}
            route={{}}
          />
        );
      case 'timesheet':
        return (
          <Timesheet
            key={`timesheet-${refreshKey}`}
            navigation={navigation}
            route={{}}
          />
        );
      default:
        return null;
    }
  };

  return (
    <TabView
      navigationState={{ index, routes }}
      renderScene={renderScene}
      onIndexChange={setIndex}
      animationEnabled={false}
      renderTabBar={props => (
        <TabBar
          {...props}
          indicatorStyle={{ backgroundColor: '#1A3C6B' }}
          style={{ backgroundColor: 'white' }}
          labelStyle={{ color: '#1A3C6B', fontWeight: '500', textTransform: 'none' }}
          inactiveColor="#888"
          activeColor="#1A3C6B"
        />
      )}
    />
  );
};

export default WorkInfoScreen;
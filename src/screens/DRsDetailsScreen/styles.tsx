import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

export default StyleSheet.create({
    container: {
        padding: 16,
        gap: 20
    },
    headerContainer: {
        backgroundColor: color.WHITE,
        flexDirection: 'row',
        alignItems: 'center',
        paddingHorizontal: 20
    },
    headerText: {
        fontSize: 18,
        color: color.BLACK
    },
    cardWrapper: {
        padding: 16,
        elevation: 2,
        backgroundColor: color.WHITE,
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        gap: 14
    },
    profileWrapper: {
        width: 120,
        aspectRatio: 1 / 1,
        borderRadius: 200,
        elevation: 4,
        backgroundColor: color.BLACK + 10,
        // borderWidth:1,
        // borderColor: color.PRIMARY_ACTIVE
    },
    DRsProfile: {
        width: 70,
        aspectRatio: 1 / 1,
        borderRadius: 200,
        elevation: 4,
        backgroundColor: color.BLACK + 10
    },
    placeHolderValueWrapper: {
        alignItems: 'center',
        width: '50%'
    },
    titleSkeleton: {
        padding: 20,
        backgroundColor: color.BLACK + 10,
        width: '60%',
        borderRadius: 20
    },
    title: {
        fontSize: 24,
        fontWeight: '600',
        color: color.BLACK
    },
    subTitle: {
        fontSize: 18,
        fontWeight: '600',
        color: color.BLACK
    },
    placeHolder: {
        fontSize: 14,
        fontWeight: '400',
        color: color.BLACK
    },
    PlaceValue: {
        fontSize: 16,
        fontWeight: '500',
        color: color.BLACK
    },
    contactContainer: {
        flexDirection: 'row',
        gap: 10,
        alignItems: "center"
    },
    PlaceValueContact: {
        fontSize: 16,
        fontWeight: '500',
        color: color.DARK_BLUE,
        width: '90%',
    },
    Button: {
        width: '100%',
        backgroundColor: color.WHITE,
        borderRadius: 8,
        borderWidth: 1,
        borderColor: color.DARK_BLUE,
        alignSelf: 'center',
        alignItems: 'center',
        justifyContent: 'center',
        flexDirection: 'row'
    },
    buttonText: {
        fontSize: 20,
        marginVertical: 10,
        color: color.DARK_BLUE,
        fontWeight: '400',
        textAlign: 'center',
        width: '80%'
    },
    mainInfoView: {
        flexWrap: 'wrap',
        gap: 5,
        width: '100%',
        alignContent: 'center',
        alignItems: 'center'
    },
    contactInfoView: {
        flexWrap: 'wrap',
        gap: 5,
        width: '100%',
        borderTopWidth: 1,
        borderColor: color.GREAYSCALE,
        paddingTop: 10,
        paddingLeft: 22
    },
    basicInfoView: {
        gap: 20,
        width: '100%',
        justifyContent: 'center',
        borderTopWidth: 1,
        borderTopColor: color.GREAYSCALE,
        paddingVertical: 10
    },
    drsProfileWrapper: {
        width: 40,
        aspectRatio: 1 / 1,
        borderRadius: 200,
        elevation: 4,
        backgroundColor: color.BLACK + 10
    },
    drsCardView: {
        width: '100%',
        justifyContent: 'center',
        borderTopWidth: 1,
        borderTopColor: color.GREAYSCALE,
        paddingVertical: 10,
        // flexDirection: 'row',
        paddingTop: 20,
        gap: 10
    },
    drsCard: {
        flexDirection: 'row',
        borderWidth: 1,
        borderColor: color.GREY_COLOR,
        borderRadius: 6,
        padding: 5,
        alignItems: 'center',
        gap: 10,
    },
    drsText: {
        fontSize: 16
    },
    titleSkeletonView: {
        padding: 8,
        width: 120,
        marginTop: 8
    },
    titleSkeletonBasicInfoView1: {
        padding: 6,
        width: 100,
        marginTop: 4
    },
    titleSkeletonBasicInfoView2: {
        padding: 8,
        width: 80,
        marginTop: 4
    },
    rowDirection: {
        flexDirection: 'row'
    },
    popupParentContainer: {
        flex: 1,
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: 'rgba(0, 0, 0, 0.5)'
    },
    popupConatiner: {
        backgroundColor: color.WHITE,
        padding: 20,
        borderRadius: 6,
        alignItems: 'center',
        width: '80%',
    },
    popupBtn: {
        marginTop: 20,
        padding: 10,
        paddingHorizontal: 40,
        backgroundColor: color.DARK_BLUE,
        borderRadius: 6
    },
    popupmsgText:{
        fontSize:14
    },
    popupBtnText: {
        color: color.WHITE,
        fontSize:14,
    }
});

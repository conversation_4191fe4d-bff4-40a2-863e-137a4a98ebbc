import React, { useState, useEffect } from 'react';
import { Linking, SafeAreaView, ScrollView, View } from 'react-native';
import { httpGet } from '../../utils/http';
import { successToast } from '../../component/SuccessToast';
import { stringText } from '../../utils/constants/stringsText';
import DRsDetailsCard from './DRsDetailsCard';
import { color } from '../../utils/constants/color';
import RNText from '../../component/RNText';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import styles from './styles';
import Entypo from 'react-native-vector-icons/Entypo';
import Popup from './noDocumentPopup';
import RNButton from '../../component/RNButton';
import apiConstant from '../../utils/constants/apiConstant';

type DRsDetailsScreenProps = {
    navigation: any;
    route: any;
};

const DRsDetailsScreen: React.FC<DRsDetailsScreenProps> = ({
    navigation,
    route
}) => {
    const [DRsData, setDRsData] = useState<any>();
    const [empId, setEmpId] = useState(route.params.userId);
    const [document, setDocument] = useState<string | null>(null);
    const [monthID, setMonthId] = useState();
    const [fetching, setFetching] = useState<boolean>(false);
    const [popupVisible, setPopupVisible] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        setFetching(true);
        httpGet(`${apiConstant.BASIC_INFO}?userId=${empId}&isGlobal=true`)
            .then((response: any) => {
                const data = JSON.parse(response)?.data;
                if (data) {
                    setDRsData(data)
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
                successToast(err?.response?.data?.message);
            });
    }, [empId]);

    useEffect(() => {
        if (empId) {
            fetchMonth()
        }
    }, []);
    useEffect(() => {
        if (monthID) {
            fetchDocument();
        }
    }, [monthID]);
    const fetchMonth = () => {
        setFetching(true);
        httpGet(`${apiConstant.ATTENDANCE_TIMESHEET}?userId=${empId}`)
            .then((response: any) => {
                const timeSheet = JSON.parse(response)?.data;
                if (timeSheet) {
                    setMonthId(timeSheet[0].id);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };
    const fetchDocument = () => {
        setFetching(true);
        httpGet(`${apiConstant.MANAGER_VIEW}?userId=${empId}&tID=${monthID}`)
            .then((response: any) => {
                const Document = JSON.parse(response)?.data;
                setDocument(Document?.userDetails?.userGoalInfo);
                setFetching(false);
            })
            .catch((err: any) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
            });
    };
    const GoToLeavesDetails = (userId: string, name: string) => {
        navigation.navigate(navigationStringText.LeavesDetails, {
            userId: userId,
            name: name
        });
    };
    const GoToAttendanceDetails = (userId: string, name: string) => {
        navigation.navigate(navigationStringText.AttendanceDetails, {
            userId: userId,
            name: name
        })
    }
    const GoToTimesheetDetails = (userId: string, name: string) => {
        navigation.navigate(navigationStringText.IDSR, {
            userId: userId,
            name: name
        })
    }
    const GoToRCADetails = (userId: string, name: string) => {
        navigation.navigate(navigationStringText.RCA, {
            userId: userId,
            name: name
        })
    }
    const GoToDocumentDetails = (userId: string, name: string) => {
        navigation.navigate(navigationStringText.Document, {
            userId: userId,
            name: name
        })
    }
    const handleClosePopup = () => {
        setPopupVisible(false);
    };
    return (
        <SafeAreaView style={{ flex: 1 }}>
            <ScrollView>
                <View style={styles.container}>
                    <DRsDetailsCard DRsData={DRsData} navigation={navigation} />
                    <RNButton
                        style={styles.Button}
                        ActiveOpacity={0.8}
                        handleOnPress={() => {
                            GoToAttendanceDetails(route.params.userId.toString(), route.params.name);
                        }}
                    >
                        <RNText style={styles.buttonText}>
                            {stringText.Attendance}
                        </RNText>
                        <Entypo
                            name="chevron-right"
                            color={color.DARK_BLUE}
                            size={24}
                        />
                    </RNButton>
                    <RNButton
                        style={styles.Button}
                        ActiveOpacity={0.8}
                        handleOnPress={() => {
                            GoToTimesheetDetails(route.params.userId.toString(), route.params.name);
                        }}
                    >
                        <RNText style={styles.buttonText}>
                            {stringText.Timesheet}
                        </RNText>
                        <Entypo
                            name="chevron-right"
                            color={color.DARK_BLUE}
                            size={24}
                        />
                    </RNButton>
                    <RNButton
                        style={styles.Button}
                        ActiveOpacity={0.8}
                        handleOnPress={() => {
                            GoToRCADetails(route.params.userId.toString(), route.params.name);
                        }}
                    >
                        <RNText style={styles.buttonText}>
                            {stringText.RCA}
                        </RNText>
                        <Entypo
                            name="chevron-right"
                            color={color.DARK_BLUE}
                            size={24}
                        />
                    </RNButton>
                    <RNButton
                        style={styles.Button}
                        ActiveOpacity={0.8}
                        handleOnPress={() => {
                            GoToLeavesDetails(route.params.userId.toString(), route.params.name);
                        }}
                    >
                        <RNText style={styles.buttonText}>
                            {stringText.Leaves}
                        </RNText>
                        <Entypo
                            name="chevron-right"
                            color={color.DARK_BLUE}
                            size={24}
                        />
                    </RNButton>
                    <RNButton
                        style={styles.Button}
                        ActiveOpacity={0.8}
                        handleOnPress={() => {
                            if (document) {
                                Linking.openURL(`${document}`);
                            }
                            else {
                                setPopupVisible(true);
                            }
                        }}
                    >
                        <RNText style={styles.buttonText}>
                            {stringText.Document}
                        </RNText>
                        <Entypo
                            name="chevron-right"
                            color={color.DARK_BLUE}
                            size={24}
                        />
                    </RNButton>
                    <Popup
                        message={'No document available'}
                        visible={popupVisible}
                        onClose={handleClosePopup}
                    />
                </View>
            </ScrollView>
        </SafeAreaView>
    );
};

export default DRsDetailsScreen;
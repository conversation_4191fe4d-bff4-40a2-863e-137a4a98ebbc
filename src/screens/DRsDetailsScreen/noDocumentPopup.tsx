import React, { FC } from 'react';
import { View, Modal } from 'react-native';
import styles from './styles';
import RNText from '../../component/RNText';
import { stringText } from '../../utils/constants/stringsText';
import RNButton from '../../component/RNButton';
type Props = {
    visible: boolean;
    message: string;
    onClose: (visible: boolean) => void;
};
const Popup: FC<Props> = (props) => {
    const { visible, message, onClose } = props;
    return (
        <Modal
            animationType="slide"
            transparent={true}
            visible={visible}
            onRequestClose={() => onClose}
        >
            <View style={styles.popupParentContainer}>
                <View style={styles.popupConatiner}>
                    <RNText style={styles.popupmsgText}>{message}</RNText>
                    <RNButton
                        handleOnPress={onClose}
                        style={styles.popupBtn}
                    >
                        <RNText style={styles.popupBtnText}>{stringText.Ok}</RNText>
                    </RNButton>
                </View>
            </View>
        </Modal>
    );
};

export default Popup;

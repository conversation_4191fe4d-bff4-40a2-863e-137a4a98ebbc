import React, { useEffect, useState } from 'react';
import { SafeAreaView, View, FlatList, Platform } from 'react-native';
import { httpGet } from '../../utils/http';
import { stringText } from '../../utils/constants/stringsText';
import RNActivityIndicator from '../../component/Loader';
import HolidayListCard from './HolidayListCard';
import SearchBar from '../../component/SearchBar';
import { Dropdown } from 'react-native-element-dropdown';
import styles from './styles';
import { color } from '../../utils/constants/color';
import RNText from '../../component/RNText';
import apiConstant from '../../utils/constants/apiConstant';

export type Props = {
    navigation: any;
};

export type HolidayListType = {
    created_at: string;
    created_by: string;
    date: string;
    deleted_at: string;
    deleted_by: string;
    desc: string;
    id: number;
    id_tenant: number;
    is_deleted: boolean;
    name: string;
    updated_at: string;
    updated_by: string;
};

const HolidayListScreen = (props: Props) => {
    const [fetching, setFetching] = useState<boolean>(false);
    const [year, setYear] = useState<string>();
    const [holidayData, setHolidayData] = useState<HolidayListType[]>([]);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [queryStr, setQueryStr] = useState<string>('');
    const [showYears, setShowYears] = useState<string[]>([]);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };
    useEffect(() => {
        setFetching(true);
        httpGet(apiConstant.HOLIDAY)
            .then((response: any) => {
                const holidayList = JSON.parse(response)?.data;
                if (holidayList) {
                    holidayList.sort(
                        (a: any, b: any) =>
                            new Date(a.date).getTime() -
                            new Date(b.date).getTime()
                    );
                    setHolidayData(holidayList);
                    const years = holidayList
                        .map((value: any, i: number) =>
                            new Date(value.date).getFullYear().toString()
                        )
                        .filter(
                            (val: string, id: string, array: any) =>
                                array.indexOf(val) === id
                        )
                        .sort((a: any, b: any) => b - a);
                    setShowYears(years);
                    setYear(years[0]);
                }
                setFetching(false);
            })
            .catch((err) => {
                console.error('Fetch error:', err);
                setFetching(false);
                errorHandlerClicked(true, `${stringText.SomethingWentwrong}`);
            });
    }, []);

    const yearCheck = (inputString: string, index: number) => {
        const dateObj = new Date(inputString);
        const dateYear = `${dateObj.getFullYear()}`;
        return dateYear;
    };

    const handleYearChange = (year: any) => {
        setYear(year.label);
    };

    const filterByYear = (holiday: HolidayListType) => {
        return year ? yearCheck(holiday.date, 0) === year : true;
    };

    const filterByTitle = (holiday: HolidayListType) => {
        const data = holiday.name.toLowerCase().includes(queryStr.toLowerCase().trim());
        return data;
    };

    const renderHolidayItem = ({ item }: { item: HolidayListType }) => (
        <HolidayListCard title={item.name} date={item.date} />
    );

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <View style={styles.searchView}>
                <SearchBar
                    handelOnChange={(searchStr) => {
                        setQueryStr(searchStr);
                    }}
                    value={queryStr}
                />
                <Dropdown
                    placeholder={year ? year : 'Select Year'}
                    style={styles.dropDown}
                    placeholderStyle={{ color: color.BLACK }}
                    itemTextStyle={{ color: color.BLACK }}
                    selectedTextStyle={{ color: color.BLACK }}
                    data={showYears.map((year: any) => ({
                        label: year,
                        value: year
                    }))}
                    value={year}
                    labelField="label"
                    valueField="value"
                    onChange={(value: any) => handleYearChange(value)}
                />
            </View>
            {holidayData.filter(filterByYear).filter(filterByTitle).length > 0 ?
                <FlatList
                    data={holidayData.filter(filterByYear).filter(filterByTitle)}
                    renderItem={renderHolidayItem}
                    contentContainerStyle={{ paddingBottom: 30 }}
                    keyExtractor={(item) => item.id.toString()}
                /> : (
                    !fetching &&
                    < View style={styles.noDataView}>
                        <RNText style={styles.noDataText}>{stringText.DataNotAvailable}</RNText>
                    </View>
                )
            }
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView >
    );
};

export default HolidayListScreen;

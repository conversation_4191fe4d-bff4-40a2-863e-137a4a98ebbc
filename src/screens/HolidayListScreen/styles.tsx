import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

const styles = StyleSheet.create({
    searchView: {
        paddingTop: 16,
        paddingHorizontal: 16,
        paddingBottom: 16,
        gap: 10
    },
    dropDown: {
        marginTop: 5,
        borderWidth: 1,
        borderColor: color.BLACK,
        justifyContent: 'center',
        paddingLeft: 10,
        paddingRight: 10,
        borderRadius: 5,
        height: 40,
        backgroundColor: color.WHITE
    },
    parentCard: {
        margin: 5,
        // backgroundColor: 'red',
        alignSelf: 'center',
        flexDirection: 'row',
        borderRadius: 10,
        // elevation: 5,
        
    },
    dateBlock: {
        width: '20%',
        backgroundColor: color.WHITE,
        alignItems: 'center',
        justifyContent: 'center',
        rowGap: -5,
        borderTopLeftRadius: 10,
        borderBottomLeftRadius: 10
    },
    verticalLine: {
        width: 0.5,
        backgroundColor: 'black',
        marginVertical: 8,
    },
    titleBlock: {
        width: '75%',
        padding: 10,
        backgroundColor: color.WHITE,
        justifyContent: 'center',
        paddingLeft: 20,
        borderTopRightRadius: 10,
        borderBottomRightRadius: 10
        // borderWidth:2,
        // borderColor:color.ACCENT_BLUE
    },
    dayText: {
        fontSize: 22,
        color: color.ACCENT_BLUE,
        fontWeight: '500'
    },
    monthText: {
        fontSize: 18,
        color: color.ACCENT_BLUE,
        fontWeight: '500'
    },
    titleText: {
        fontSize: 18,
        color: color.BLACK,
        fontWeight: '500'
    },
    weekDayText: {
        fontSize: 16,
        color: color.BLACK,
        fontWeight: '400'
    },
    noDataView : {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center'
    },
    noDataText: {
        paddingLeft: 30,
        paddingVertical: 10,
        fontWeight: 'bold',
        alignSelf:'center'
    }
});
export default styles;

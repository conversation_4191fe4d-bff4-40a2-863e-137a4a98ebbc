import React, { SetStateAction } from 'react';
import { <PERSON><PERSON>, SafeAreaView, View } from 'react-native';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import RNImage from '../../component/RNImage';
import RNText from '../../component/RNText';
import { color } from '../../utils/constants/color';
import { stringText } from '../../utils/constants/stringsText';
import styles from '../AttendanceScreen/styles';
import Spinner from '../../component/Loader';
import RNButton from '../../component/RNButton';

export type Props = {
    setSelectedButton: React.Dispatch<SetStateAction<number>>;
    selectedButton: number;
    InAttedanceGenerate: () => void;
    attendanceInButtonClicked: string;
    OutAttedanceGenerate: () => void;
    attendanceOutButtonClicked: string;
    errorHandlerVisibility: boolean;
    errorHandlerMessage: string;
    qrCodeTimer: number;
    QRCodeError: boolean;
    errorHandlerClicked: (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => void;
    loader: boolean;
};

const AttendanceView = (props: Props) => {
    const {
        setSelectedButton,
        selectedButton,
        InAttedanceGenerate,
        attendanceInButtonClicked,
        OutAttedanceGenerate,
        attendanceOutButtonClicked,
        errorHandlerVisibility,
        errorHandlerMessage,
        errorHandlerClicked,
        qrCodeTimer,
        QRCodeError,
        loader
    } = props;

    return (
        <SafeAreaView style={styles.mainView}>
            <RNText style={styles.textHead}>{stringText.GenerateQRcode}</RNText>
            <View style={styles.buttonView}>
                <RNButton
                    style={[
                        styles.InButton,
                        {
                            backgroundColor:
                                selectedButton == 0
                                    ? color.DARK_BLUE
                                    : color.WHITE
                        }
                    ]}
                    ActiveOpacity={loader ? 1 : 0.6}
                    handleOnPress={() => {
                        if (loader) {
                        } else {
                            setSelectedButton(0);
                            InAttedanceGenerate();
                        }
                    }}
                >
                    <RNText
                        style={[
                            styles.InButtonText,
                            {
                                color:
                                    selectedButton == 0
                                        ? color.WHITE
                                        : color.DARK_BLUE
                            }
                        ]}
                    >
                        {stringText.InText}
                    </RNText>
                </RNButton>
                <RNButton
                    style={[
                        styles.OutButton,
                        {
                            backgroundColor:
                                selectedButton == 1
                                    ? color.DARK_BLUE
                                    : color.WHITE
                        }
                    ]}
                    ActiveOpacity={loader ? 1 : 0.6}
                    handleOnPress={() => {
                        if (loader) {
                            // Alert.alert('api call not completed');
                        } else {
                            setSelectedButton(1);
                            OutAttedanceGenerate();
                        }
                    }}
                >
                    <RNText
                        style={[
                            styles.OutButtonText,
                            {
                                color:
                                    selectedButton == 1
                                        ? color.WHITE
                                        : color.DARK_BLUE
                            }
                        ]}
                    >
                        {stringText.OutText}
                    </RNText>
                </RNButton>
            </View>
            <RNText style={styles.textHead}>{stringText.QRcodeText}</RNText>
            <View style={styles.ImgView}>
                {!loader &&
                    (attendanceInButtonClicked ||
                        attendanceOutButtonClicked) && (
                        <RNImage
                            source={{
                                uri: !selectedButton
                                    ? attendanceInButtonClicked
                                    : attendanceOutButtonClicked
                            }}
                            style={styles.qrImg}
                        />
                    )}
                <Spinner animating={loader} />
            </View>
            <RNText style={styles.timerText}>
                QR code will expire in
                <RNText
                    style={{
                        color: color.DARK_RED,
                        fontSize: 18,
                        fontWeight: 'bold'
                    }}
                >
                    {' '}
                    {!loader && props.qrCodeTimer >= 0 && !props.QRCodeError
                        ? props.qrCodeTimer
                        : '--'}{' '}
                </RNText>
                seconds
            </RNText>
            <ErrorHandlerPopup
                visible={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
        </SafeAreaView>
    );
};

export default AttendanceView;

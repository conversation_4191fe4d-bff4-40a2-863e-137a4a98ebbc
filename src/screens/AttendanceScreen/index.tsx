import React, { useEffect, useRef, useState } from 'react';
import AttendanceView from './AttendanceView';
import { httpPost } from '../../utils/http';
import Geolocation from 'react-native-geolocation-service';
import moment from 'moment';
import { stringText } from '../../utils/constants/stringsText';
import { useFocusEffect, useNavigation } from '@react-navigation/native';
import { AppState, BackHandler, Platform } from 'react-native';
import { successToast } from '../../component/SuccessToast';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import AsyncStorage from '@react-native-async-storage/async-storage';

const AttendanceScreen = () => {
    const [selectedButton, setSelectedButton] = useState<number>(0);
    const [attendanceInButtonClicked, setAttendanceInButtonClicked] =
        useState<string>('');
    const [attendanceOutButtonClicked, setAttendanceOutButtonClicked] =
        useState<string>('');
    const [datetoday, setDatetoday] = useState<Date>(new Date());
    const [loader, setLoader] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [qrStatus, setQRStatus] = useState<string>('IN');
    const [qrCodeTimer, setQrCodeTimer] = useState<number>(30);
    const [qrCodeAPITimer, setQrCodeAPITimer] = useState<number>(30);
    const [QRCodeError, setQRCodeError] = useState<boolean>(false);
    const appState = useRef(AppState.currentState);
    const [appStateVisible, setAppStateVisible] = useState(appState.current);
    const [changeLocation, setChangeLocation] = useState<boolean>(false);
    const [locationWatchId, setLocationWatchId] = useState<number | null>(null);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        setQRStatus('IN');
        InAttedanceGenerate();
    }, []);

    // Cleanup watchPosition on component unmount
    useEffect(() => {
        return () => {
            if (locationWatchId !== null) {
                Geolocation.clearWatch(locationWatchId);
            }
        };
    }, [locationWatchId]);


    useEffect(() => {
        const subscription = AppState.addEventListener(
            'change',
            (nextAppState) => {
                if (
                    appState.current.match(/inactive|background/) &&
                    nextAppState === 'active' && !changeLocation
                ) {
                    setLoader(false);
                    setSelectedButton(0);
                    InAttedanceGenerate();
                }

                appState.current = nextAppState;
                setAppStateVisible(appState.current);
            }
        );

        return () => {
            subscription.remove();
        };
    }, [changeLocation]);

    useEffect(() => {
        const interval = setInterval(() => {
            setQrCodeTimer((prevTimer) => {
                if (prevTimer > 0) {
                    return prevTimer - 1;
                } else {
                    return 0;
                }
            });
        }, 1000);

        return () => clearInterval(interval);
    }, [qrCodeAPITimer]);
    useFocusEffect(
        React.useCallback(() => {
            if (qrCodeTimer == 0) {
                if (qrStatus == 'IN') {
                    InAttedanceGenerate();
                } else {
                    OutAttedanceGenerate();
                }
            }
        }, [qrCodeTimer])
    );

    const InAttedanceGenerate = async () => {
        // Clear any existing watchPosition first
        if (locationWatchId !== null) {
            console.log('Clearing existing watchPosition:', locationWatchId);
            Geolocation.clearWatch(locationWatchId);
            setLocationWatchId(null);
        }

        const userVersion = await AsyncStorage.getItem("user_version");
        const playstoreVersion = await AsyncStorage.getItem("playstore_version");
        setAttendanceInButtonClicked(
            ""
        );
        setLoader(true);
        setChangeLocation(true);

        console.log('Starting new watchPosition for IN attendance');
        const watchId = Geolocation.watchPosition(
            (position) => {
                console.log('NEW watchPosition log - IN attendance');
                console.log('watchPosition latitude:', position.coords.latitude);
                console.log('watchPosition longitude:', position.coords.longitude);

                // Process the location for attendance
                const location = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude
                };

                const payload = {
                    order_type: '1',
                    order_date: `${moment(datetoday).format('YYYY-MM-DD')}`,
                    latitude: `${location?.latitude}`,
                    longitude: `${location?.longitude}`,
                    user_version: userVersion?.toString(),
                    playstore_version: playstoreVersion?.toString()
                };

                httpPost('/generate_attendance_qr', payload)
                    .then(async (response: any) => {
                        setQRCodeError(false);
                        setChangeLocation(false);
                        setAttendanceInButtonClicked(
                            JSON.parse(response)?.data?.file_name
                        );
                        setQrCodeAPITimer(
                            JSON.parse(response)?.data.expired_in ?? 30
                        );
                        setQrCodeTimer(JSON.parse(response)?.data.expired_in ?? 30);
                        setLoader(false);

                        // Clear the watch after successful API call
                        console.log('Clearing watchPosition after successful API call');
                        Geolocation.clearWatch(watchId);
                        setLocationWatchId(null);
                    })
                    .catch((err) => {
                        setLoader(false);
                        // Clear the watch on error
                        console.log('Clearing watchPosition due to API error');
                        Geolocation.clearWatch(watchId);
                        setLocationWatchId(null);

                        if (
                            err?.response?.data?.message !== undefined &&
                            err?.response?.data?.message !== null &&
                            err?.response?.data?.message !== ''
                        ) {
                            setQRCodeError(true);
                            errorHandlerClicked(
                                true,
                                err?.response?.data?.message
                            );
                        } else {
                            errorHandlerClicked(
                                true,
                                `${stringText.SomethingWentwrong}`
                            );
                        }
                    });
            },
            (error) => {
                console.log('watchPosition error:', error);
                setLoader(false);
                setQRCodeError(true);
                errorHandlerClicked(true, `${stringText.ConfirmLocation}`);
                Geolocation.clearWatch(watchId);
                setLocationWatchId(null);
            },
            {
                enableHighAccuracy: true,
                distanceFilter: 0.1,
                interval: 1000,
                fastestInterval: 100,
            }
        );

        console.log('New watchPosition started with ID:', watchId);
        setLocationWatchId(watchId);

    };

    const OutAttedanceGenerate = async () => {
        // Clear any existing watchPosition first
        if (locationWatchId !== null) {
            console.log('Clearing existing watchPosition:', locationWatchId);
            Geolocation.clearWatch(locationWatchId);
            setLocationWatchId(null);
        }

        const userVersion = await AsyncStorage.getItem("user_version");
        const playstoreVersion = await AsyncStorage.getItem("playstore_version");
        setAttendanceOutButtonClicked(
            "");
        setLoader(true);
        setChangeLocation(true);

        console.log('Starting new watchPosition for OUT attendance');
        const watchId = Geolocation.watchPosition(
            (position) => {
                console.log('NEW watchPosition log - OUT attendance');
                console.log('watchPosition latitude:', position.coords.latitude);
                console.log('watchPosition longitude:', position.coords.longitude);

                // Process the location for attendance
                const location = {
                    latitude: position.coords.latitude,
                    longitude: position.coords.longitude
                };

                const payload = {
                    order_type: '2',
                    order_date: `${moment(datetoday).format('YYYY-MM-DD')}`,
                    latitude: `${location?.latitude}`,
                    longitude: `${location?.longitude}`,
                    user_version: userVersion?.toString(),
                    playstore_version: playstoreVersion?.toString()
                };

                httpPost('/generate_attendance_qr', payload)
                    .then(async (response: any) => {
                        setQRStatus('OUT');
                        setQRCodeError(false);
                        setChangeLocation(false);
                        setAttendanceOutButtonClicked(
                            JSON.parse(response)?.data?.file_name
                        );

                        setQrCodeAPITimer(
                            JSON.parse(response)?.data.expired_in ?? 30
                        );
                        setQrCodeTimer(JSON.parse(response)?.data.expired_in ?? 30);
                        setLoader(false);

                        // Clear the watch after successful API call
                        console.log('Clearing watchPosition after successful API call');
                        Geolocation.clearWatch(watchId);
                        setLocationWatchId(null);
                    })
                    .catch((err) => {
                        setLoader(false);
                        // Clear the watch on error
                        console.log('Clearing watchPosition due to API error');
                        Geolocation.clearWatch(watchId);
                        setLocationWatchId(null);

                        if (
                            err?.response?.data?.message !== undefined &&
                            err?.response?.data?.message !== null &&
                            err?.response?.data?.message !== ''
                        ) {
                            setQRCodeError(true);
                            errorHandlerClicked(
                                true,
                                err?.response?.data?.message
                            );
                        } else {
                            errorHandlerClicked(
                                true,
                                `${stringText.SomethingWentwrong}`
                            );
                        }
                    });
            },
            (error) => {
                console.log('watchPosition error (OUT):', error);
                setLoader(false);
                setQRCodeError(true);
                errorHandlerClicked(true, `${stringText.ConfirmLocation}`);
                Geolocation.clearWatch(watchId);
                setLocationWatchId(null);
            },
            {
                enableHighAccuracy: true,
                distanceFilter: 0.1,
                interval: 1000,
                fastestInterval: 100,
            }
        );

        console.log('New watchPosition started with ID:', watchId);
        setLocationWatchId(watchId);
    };
    const navigation = useNavigation();
    useEffect(() => {
        const backAction = () => {
            navigation.navigate(navigationStringText.Dashboard);
            return true; // Prevent default behavior (exit the app)
        };

        const backHandler = BackHandler.addEventListener(
            'hardwareBackPress',
            backAction
        );

        return () => backHandler.remove();
    }, [navigation]);

    return (
        <AttendanceView
            QRCodeError={QRCodeError}
            qrCodeTimer={qrCodeTimer}
            setQRStatus={setQRStatus}
            setSelectedButton={setSelectedButton}
            selectedButton={selectedButton}
            InAttedanceGenerate={InAttedanceGenerate}
            attendanceInButtonClicked={attendanceInButtonClicked}
            OutAttedanceGenerate={OutAttedanceGenerate}
            errorHandlerVisibility={errorHandlerVisibility}
            errorHandlerMessage={errorHandlerMessage}
            errorHandlerClicked={errorHandlerClicked}
            attendanceOutButtonClicked={attendanceOutButtonClicked}
            loader={loader}
        />
    );
};

export default AttendanceScreen;

import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

const styles = StyleSheet.create({
    parentContainer: {
        flex: 1
    },
    newTabBtn: {
        margin: 10,
        padding: 10,
        borderWidth: 1,
        borderRadius: 8,
        backgroundColor: color.DARK_BLUE,
        alignSelf: 'flex-end',
        flexDirection: 'row'
    },
    newTabText: {
        color: color.WHITE,
        fontSize: 18,
        fontWeight: '600',
        textAlign: 'center'
    },
    webviewContainer: {
        backgroundColor: color.WHITE,
        flex: 1,
        marginBottom: 10,
        marginHorizontal: 15
    },
    webView: {
        height: '90%',
        width: '100%',
    }
});
export default styles;

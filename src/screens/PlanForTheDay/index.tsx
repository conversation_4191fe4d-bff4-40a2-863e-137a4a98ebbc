import React, { useEffect, useState } from 'react';
import { SafeAreaView, View, FlatList } from 'react-native';
import { httpGet } from '../../utils/http';
import { stringText } from '../../utils/constants/stringsText';
import RNActivityIndicator from '../../component/Loader';
import { Dropdown } from 'react-native-element-dropdown';
import { color } from '../../utils/constants/color';
import RNText from '../../component/RNText';
import apiConstant from '../../utils/constants/apiConstant';
import PlanForTheDayListCard from './PlanForTheDayCard';
import styles from './style';

export type Props = {
    navigation: any;
    route: any;
};

export type PlanForTheDayType = {
    // id: number;
    // to: string;
    // cc: string;
    // sender_mail: string;
    // mandate_type_id:string;
    // priority: string;
    time_spent: string;
    project_name: string;
    subject: string;
    PLANFORDAY_date: string;
    body: string;
    other_project_name: string;
    // comment: string;
    // approve_status: number;
    // submission_time: string;
    // comment_submission_time: string;
};

export interface MonthData {
    id: number;
    start_date: string;
}

const PlanForTheDayScreen = (props: Props) => {
    const [fetching, setFetching] = useState<boolean>(false);
    const [userId, setUserId] = useState<number | null>(props?.route?.params?.userId);
    const [monthPlanForTheDay, setMonthPlanForTheDay] = useState<MonthData[]>([]);
    const [monthID, setMonthId] = useState();
    const [PlanForTheDayData, setPlanForTheDayData] = useState<PlanForTheDayType[]>([]);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);
    const [monthData, setMonthData] = useState<MonthData[]>([]);

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        if (!(props?.route?.params?.userId)) {
            fetchUserId();
        }
        else if (userId) {
            fetchMonth();
        }
    }, []);

    useEffect(() => {
        if (userId) {
            fetchMonth();
        }
    }, [userId]);

    useEffect(() => {
        if (monthID) {
            fetchPlanForTheDays();
        }
    }, [monthID]);

    const fetchUserId = () => {
        setFetching(true);
        httpGet(apiConstant.USER_INFO)
            .then((response: any) => {
                const UserId = JSON.parse(response)?.data;
                if (UserId) {
                    setUserId(UserId?.id);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const fetchMonth = () => {
        setFetching(true);
        httpGet(`${apiConstant.ATTENDANCE_TIMESHEET}?userId=${userId}`)
            .then((response: any) => {
                const PlanForTheDay = JSON.parse(response)?.data;
                if (PlanForTheDay) {
                    setMonthPlanForTheDay(PlanForTheDay);
                    setMonthId(PlanForTheDay[0].id);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const fetchPlanForTheDays = () => {
        setFetching(true);
        httpGet(`${apiConstant.PlanForTheDay}?userId=${userId}&tId=${monthID}`)
            .then((response: any) => {
                const PlanForTheDayData = JSON.parse(response)?.data;
                if (PlanForTheDayData) {
                    setPlanForTheDayData(PlanForTheDayData);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                if (
                    err?.response?.data?.message !== undefined &&
                    err?.response?.data?.message !== null &&
                    err?.response?.data?.message !== ''
                ) {
                    errorHandlerClicked(true, err?.response?.data?.message);
                } else {
                    errorHandlerClicked(
                        true,
                        `${stringText.SomethingWentwrong}`
                    );
                }
            });
    };

    const handleMonthChange = (month: any) => {
        setMonthId(month);
    };

    const filterByMonth = (planForTheDay: PlanForTheDayType) => {
        if (!monthID) return true;
        const planForTheDayMonth = new Date(planForTheDay.PLANFORDAY_date).getMonth() + 1;
        const selectedMonth = parseInt(monthID);
        return planForTheDayMonth === selectedMonth;
    };

    const renderPlanForTheDayItem = ({ item }: { item: PlanForTheDayType }) => {
        return (
            <PlanForTheDayListCard
                timeSpent={item.time_spent}
                projectName={item.project_name}
                subject={item.subject}
                date={item.PLANFORDAY_date}
                body={item.body}
                otherProjectName={item.other_project_name}
            />
        );
    };

    const getMonthName = (dateString: string): string => {
        const date = new Date(dateString);
        const monthIndex = date.getMonth();
        const year = date.getFullYear();
        const months = [
            'January',
            'February',
            'March',
            'April',
            'May',
            'June',
            'July',
            'August',
            'September',
            'October',
            'November',
            'December'
        ];
        return `${months[monthIndex]} ${year}`;
    };
    return (
        <SafeAreaView
            style={styles.mainContainer}
        >
            <View style={styles.searchView}>
                <Dropdown
                    placeholder={monthID ? monthID : 'Select Month'}
                    style={styles.dropDown}
                    placeholderStyle={{ color: color.BLACK }}
                    itemTextStyle={{ color: color.BLACK }}
                    selectedTextStyle={{ color: color.BLACK }}
                    data={monthPlanForTheDay.map((month: any) => ({
                        label: getMonthName(month.start_date),
                        value: month.id
                    }))}
                    value={monthID}
                    labelField="label"
                    valueField="value"
                    onChange={(value: any) => handleMonthChange(value.value)}
                />
            </View>
            {PlanForTheDayData.length > 0 && !fetching ? (
                <FlatList
                    data={PlanForTheDayData}
                    renderItem={renderPlanForTheDayItem}
                    keyExtractor={(item) => item.PLANFORDAY_date.toString()}
                    contentContainerStyle={{ paddingBottom: 30 }}
                    showsVerticalScrollIndicator={false}
                />
            ) : (
                !fetching && (
                    <View
                        style={styles.noDataView}
                    >
                        <RNText style={styles.noDataText}>{stringText.DataNotAvailable}</RNText>
                    </View>
                )
            )}
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    );
};

export default PlanForTheDayScreen;

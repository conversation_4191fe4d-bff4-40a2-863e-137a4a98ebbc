import React, { useEffect, useState } from "react";
import { httpGet } from "../../utils/http";
import { stringText } from "../../utils/constants/stringsText";
import { SafeAreaView, ScrollView, View } from "react-native";
import { Dropdown } from "react-native-element-dropdown";
import styles from "./styles";
import { color } from "../../utils/constants/color";
import NoAttendanceDetailsView from "./NoAttendanceTable";
import LateAttendanceTable from "./LateAttendanceTable";
import RNText from "../../component/RNText";
import moment from "moment";
import RNActivityIndicator from "../../component/Loader";
import apiConstant from "../../utils/constants/apiConstant";


export type Props = {
    navigation: any;
    route: any;
}


const AttendanceDetailsScreen: React.FC<Props> = ({ navigation, route }) => {

    const [userId, setUserId] = useState<any>(route?.params?.userId);
    const [selectedMonth, setSelectedMonth] = useState<string>();
    const [monthTimesheet, setMonthTimesheet] = useState<any[]>([]);
    const [monthID, setMonthId] = useState<any>();
    const [lateAttendance, setLateAttendance] = useState<any>();
    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);
    const [reportTiming, setReportTiming] = useState<any>();

    let formattedTime = moment(lateAttendance?.officeTiming, 'HH:mm:ss').format('h:mm A');

    function convertTime(timeString: string) {
        if (!timeString) {
            return 'Invalid time';
        }
        else if (timeString.includes(':')) {
            const [hours, minutes] = timeString.split(':');
            let formattedHours = parseInt(hours);
            const period = formattedHours >= 12 ? 'PM' : 'AM';

            formattedHours = formattedHours % 12 || 12;

            const formattedMinutes = parseInt(minutes) < 10 ? '0' + parseInt(minutes) : minutes;

            return `${formattedHours}:${formattedMinutes} ${period}`;
        }
        else if (timeString.includes('T')) {
            const date = new Date(timeString);
            date.setHours(date.getHours() - 5);
            date.setMinutes(date.getMinutes() - 30);
            let hours = date.getHours();
            const minutes = date.getMinutes();
            const period = hours >= 12 ? 'PM' : 'AM';

            hours = hours % 12 || 12;

            const formattedMinutes = minutes < 10 ? '0' + minutes : minutes;

            return `${hours}:${formattedMinutes} ${period}`;
        }
        else {
            return timeString;
        }
    }


    const errorHandlerClicked = (
        visibility: boolean,
        message: string
    ) => {
        setErrorHandlerVisibility(visibility);
        setErrorHandlerMessage(message);
    };

    const currentYear = new Date().getFullYear();
    const currentYearData = monthTimesheet.filter(item => {
        const year = new Date(item.start_date).getFullYear();
        return year === currentYear;
    });
    const convertDateToMonthYear = (dateString: string) => {
        const date = new Date(dateString);
        return date.toLocaleString('default', { month: 'long', year: 'numeric' });
    };
    useEffect(() => {
        if (!(route?.params?.userId)) {
            fetchUserId();
        }
        if (userId) {
            fetchMonth();
        }
    }, []);

    useEffect(() => {
        if (userId) {
            fetchMonth();
        }
    }, [userId]);

    useEffect(() => {
        fetchLateAttendance();
    }, [monthID]);

    const fetchUserId = () => {
        setFetching(true);
        httpGet(apiConstant.USER_INFO)
            .then((response: any) => {
                const UserId = JSON.parse(response)?.data;
                if (UserId) {
                    setUserId(UserId?.id);
                    fetchMonth();
                } else {
                    setFetching(false);
                }
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
            });
    };

    const fetchMonth = () => {
        httpGet(`${apiConstant.ATTENDANCE_TIMESHEET}?userId=${userId}`)
            .then((response: any) => {
                const timeSheet = JSON.parse(response)?.data;
                if (timeSheet) {
                    setMonthTimesheet(timeSheet);
                    setMonthId(timeSheet[0].id);
                    fetchLateAttendance();
                } else {
                    setFetching(false);
                }
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
            });
    };

    const fetchLateAttendance = () => {
        httpGet(`${apiConstant.ATTENDANCE}?userId=${userId}&tID=${monthID}`)
            .then((response: any) => {
                const attendance = JSON.parse(response)?.data;
                if (attendance) {
                    setLateAttendance(attendance);
                    setReportTiming(convertTime(lateAttendance.officeTime));
                    setFetching(false);
                } else {
                    setFetching(false);
                }
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
            });
    };

    return (
        <SafeAreaView style={styles.parentContainer}>
                <View>
                    <Dropdown
                        placeholder={
                            currentYearData.length<=0 ? "-": selectedMonth ? selectedMonth : convertDateToMonthYear(currentYearData[0]?.start_date)}
                        style={styles.dropDown}
                        placeholderStyle={{ color: color.BLACK }}
                        itemTextStyle={{ color: color.BLACK }}
                        selectedTextStyle={{ color: color.BLACK }}
                        data={currentYearData.map(item => ({
                            label: convertDateToMonthYear(item.start_date),
                            value: item.id,
                        }))}
                        value={selectedMonth}
                        labelField="label"
                        valueField="value"
                        onChange={(item) => {
                            setSelectedMonth(item.label);
                            setMonthId(item.value)
                        }}
                    />
                    <RNText style={styles.reportTimeTitle}>
                        {stringText.Reportingtime}: {(fetching || formattedTime.toString() ===
                            'Invalid date' || formattedTime.trim()=='') ? "--" : formattedTime ? formattedTime : "--"}
                    </RNText>
                </View>
            <ScrollView showsVerticalScrollIndicator={false} >

                <LateAttendanceTable
                    lateAttendance={lateAttendance}
                />
                <NoAttendanceDetailsView
                    lateAttendance={lateAttendance}
                />
            </ScrollView>
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    );
};

export default AttendanceDetailsScreen;

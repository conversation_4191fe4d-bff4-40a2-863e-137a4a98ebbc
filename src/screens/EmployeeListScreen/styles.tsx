import { StyleSheet } from "react-native";
import { color } from "../../utils/constants/color";

const styles = StyleSheet.create({
    mainContainer: {
        paddingTop: 16,
        paddingHorizontal: 16,
        paddingBottom: 16
    },
    searchEmployeeButtonText: {
        borderWidth: 1,
        paddingHorizontal: 10,
        borderRadius: 5,
        borderColor: color.DARK_BLUE,
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
        margin: 10
    },
    employeeListButtonText: {
        textAlign: 'left',
        fontSize: 15,
        fontFamily: 'Poppins-Regular',
        flex: 1,
        color:color.BLACK
    },
    empListCardWrapper: {
        flexDirection: "row",
        backgroundColor: color.WHITE,
        margin:5,
        paddingVertical:10,
        marginHorizontal:15,
        borderRadius:10
    },
    proileImgWrapper: {
        width:"25%",
        justifyContent:'center',
        alignItems:'center',
        borderRadius:10,
        backgroundColor:color.WHITE,
        marginLeft:10
    },
    proileImg: {
        height: 80,
        width: 80,
        borderRadius: 40,
        backgroundColor:color.WHITE,
    },
    empIdText:{
        fontWeight:'bold',
        fontSize:16,
    },
    detailsWrapper:{
        margin:10,
        width:'70%'
    },
    nameText: {
        fontSize: 18,
        color: color.BLACK,
        fontWeight:'500',
        // flexWrap:'wrap',
        width:"95%",
    },
    designationText: {
        fontSize: 14,
        color: color.BLACK,
        fontStyle: 'italic',
        margin:2,
        width:'90%'
    },
    iconDetailsWrapper: {
        flexDirection: 'row',
        alignItems:'center',
    },
    iconTextText:{
        fontSize: 14,
        color: color.BLACK,
        fontStyle: 'italic',
        marginLeft:5,
        width:'85%'
    },
    noDataView: {
        flex: 1,
        alignItems: 'center',
        justifyContent: 'center',
    },
    titleText: {
        fontSize: 16,
        fontWeight: '500',
        color: color.BLACK,
        flexWrap: 'wrap',
        padding:12,
        paddingHorizontal:20
    }

})

export default styles;
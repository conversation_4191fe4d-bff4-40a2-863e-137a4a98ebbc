import { StyleSheet } from 'react-native';
import DrsOnLeaveView from './DRsOnLeaveView';
import { color } from '../../utils/constants/color';
import { Dropdown } from 'react-native-element-dropdown';

const styles = StyleSheet.create({
    scrollContainer: {},
    parentContainer: {
        justifyContent: 'center'
    },
    headerText: {
        textAlign: 'center',
        color: color.BLACK,
        fontWeight: 'bold',
        fontSize: 18,
        paddingTop: 16
    },
    searchService: {
        borderWidth: 1,
        paddingHorizontal: 10,
        borderRadius: 5,
        borderColor: color.DARK_BLUE,
        flexDirection: 'row',
        alignItems: 'center',
        gap: 10,
        marginVertical: 10
    },
    dropDownContainer: {
        flexDirection: 'row',
        justifyContent: 'space-around',
        gap: 10,
        padding:16
    },
    dropdown: {
        borderWidth: 1,
        flex: 1,
        borderRadius: 5,
        padding: 5,
        marginBottom: 20,
        backgroundColor:color.WHITE
    },

    // Service Requests Card style

    serviceRequestsCard: {
        backgroundColor: color.WHITE,
        marginVertical: 6,
        elevation: 4,
        borderRadius: 6,
        paddingVertical: 16,
        flexDirection: 'row',
        justifyContent: 'space-between',
        alignItems: 'center',
        position: 'relative'
    },
    flexCenter: {
        alignItems: 'center',
        gap: 10
    },
    serviceRequestsCardLeftContent: {
        flexDirection: 'row',
        gap: 20,
        paddingHorizontal: 12,
        maxWidth: '72%',
    },
    srIdWrapper: {
        padding: 10,
        borderRadius: 6,
        backgroundColor: color.DARK_BLUE
    },
    srIdText: {
        fontSize: 16,
        fontWeight: '600',
        letterSpacing: 2,
        color: color.WHITE
    },
    priorityWrapper: {
        width:'100%',
        padding: 4,
        alignItems: 'center',
        flex: 1,
        flexDirection:'row',
    },
    priorityText: {
        fontSize: 14,
        fontWeight: '500',
        color: color.BLACK
    },
    srTitleWrapper: {
        flex: 1,
        gap: 6,
        justifyContent: 'center'
    },
    nameText: {
        fontSize: 18,
        fontWeight: '500',
        color: color.BLACK,
        flexWrap: 'wrap'
    },
    noDataView: {
        flex: 1,
        marginTop: 100,
        alignItems: 'center',
        justifyContent: 'center'
    },
    titleText: {
        fontSize: 15,
        fontWeight: '400',
        color: color.BLACK,
        flexWrap: 'wrap'
    },
    departmentText: {
        fontSize: 14,
        fontWeight: '400',
        color: color.BLACK
    },
    srTypeText: {
        fontSize: 12,
        fontWeight: '400',
        color: color.BLACK
    },
    // lineBreak: {
    //     width: 1,
    //     height: '100%',
    //     backgroundColor: color.BLACK + 20
    // },
    serviceRequestsCardRightContent: {
        flexDirection: 'column',
        alignItems: 'center',
        flex: 1,
    },
    createdByText: {
        fontSize: 12,
        fontWeight: '400',
        color: color.BLACK
    },
    createdAtText: {
        fontSize: 12,
        fontWeight: '400',
        color: color.BLACK,
        textAlign: 'center'
    }
});

export default styles;

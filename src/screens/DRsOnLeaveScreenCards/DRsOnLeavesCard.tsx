import React, { useState, SetStateAction, Dispatch, useEffect } from 'react';
import { View } from 'react-native';
import RNText from '../../component/RNText';
import styles from './style';
import Entypo from 'react-native-vector-icons/Entypo';
import { color } from '../../utils/constants/color';
import ActionStatusPopUpView from '../ActionStatusPopUp/ActionStatusPopUpView';
import { stringText } from '../../utils/constants/stringsText';
import RNActivityIndicator from '../../component/Loader';
import RNButton from '../../component/RNButton';

export type Props = {
    navigation: any;
    setActionPopupVisibility: Dispatch<SetStateAction<boolean>>;
    actionButtonClicked: (isVisible: boolean) => void;
    isVisible: boolean;
    data: {
        desc: string;
        id: number;
        id_employee: number;
        id_leave_type: number;
        leave_end_date: string;
        leave_start_date: string;
        name: string;
        service_request_id: number;
        status: number;
        service_request_title:string
        
    };
    postLeaveStatus: (leaveId: number, leaveStatus: number) => void;
};

const DRsOnLeavesCard = (props: Props) => {
    const {
        isVisible,
        actionButtonClicked,
        setActionPopupVisibility,
        navigation,
        postLeaveStatus,
        data
        
    } = props;
    const [popupVisibility, setPopupVisibility] = useState<boolean>(false);
    const [userData,setUserData] = useState()
    const [empName, setEmpName] = useState<string>('');
    const [message, setMessage] = useState<string>("")

    const handelStatusChange = (data: any, message: string) => {
        setUserData(data)
        setPopupVisibility(!popupVisibility),
        setMessage(message)
    }

    useEffect(()=>{
        
    })

    return (
        <View style={{ paddingHorizontal: 16 }}>
            <View style={styles.serviceRequestsCard}>
                <View style={styles.serviceRequestsCardLeftContent}>
                    <View style={styles.flexCenter}>
                        <View style={styles.srIdWrapper}>
                            <RNText style={styles.srIdText}>
                                {data.id_employee}
                            </RNText>
                        </View>
                        <View style={[styles.priorityWrapper]}>
                            {data.status == 0 ? (
                                <>
                                    <RNText style={{ color: color.BLACK }}>Pending</RNText>
                                </>
                            ) : data.status == 1 ? (
                                <>
                                    <RNText style={{ color: color.DARK_GREEN }}>Accepted</RNText>
                                </>
                            ) : (
                                <>
                                    <RNText style={{ color: color.DARK_RED }}>Rejected</RNText>
                                </>
                            )}
                        </View>
                    </View>
                    <View style={styles.srTitleWrapper}>
                        <RNText style={styles.nameText}>{data.name}</RNText>
                        {/* <RNText style={styles.titleText}>{data.service_request_title}</RNText> */}
                        <RNText style={styles.srTypeText}>
                            Start: {data.leave_start_date
                                .slice(0, 10)
                                .replace(/-/g, '/')}
                        </RNText>
                        <RNText style={styles.srTypeText}>
                            End: {data.leave_end_date
                                .slice(0, 10)
                                .replace(/-/g, '/')}
                        </RNText>
                    </View>
                </View>
                {data.status == 0 && (
                    <View style={styles.serviceRequestsCardRightContent}>
                        <RNButton
                            handleOnPress={() => (
                                handelStatusChange(data, stringText.AreYouSureWantToReject)
                            )}
                            style={[styles.priorityWrapper]}
                        >
                            <Entypo
                                name="cross"
                                style={{
                                    color: color.DARK_RED,
                                    marginRight: 10
                                }}
                                size={20}
                            />
                            <RNText style={{ color: color.DARK_RED }}>Reject</RNText>
                        </RNButton>
                        <RNButton
                            handleOnPress={() => (
                                handelStatusChange(data, stringText.AreYouSureWantToAccept)
                            )}
                            style={[styles.priorityWrapper]}
                        >
                            <Entypo
                                name="check"
                                style={{
                                    color: color.DARK_GREEN,
                                    marginRight: 10
                                }}
                                size={20}
                            />
                            <RNText style={{ color: color.DARK_GREEN }}>Accept</RNText>
                        </RNButton>
                    </View>
                )}
                {data.status == 1 && (
                    <RNButton
                        handleOnPress={() => (
                            handelStatusChange(data, stringText.AreYouSureWantToReject)
                        )}
                        style={[styles.priorityWrapper]}
                    >
                        <Entypo
                            name="cross"
                            style={{
                                color: color.DARK_RED,
                                marginRight: 2
                            }}
                            size={20}
                        />
                        <RNText style={{ color: color.DARK_RED }}>
                            Reject
                        </RNText>
                    </RNButton>
                )}
                {data.status !== 1 && data.status !== 0 && (
                    <RNButton
                        handleOnPress={() => (
                            handelStatusChange(data, stringText.AreYouSureWantToAccept)
                        )}
                        style={[styles.priorityWrapper]}
                    >
                        <Entypo
                            name="check"
                            style={{
                                color: color.DARK_GREEN,
                                marginRight: 2
                            }}
                            size={20}
                        />
                        <RNText style={{ color: color.DARK_GREEN }}>
                            Accept
                        </RNText>
                    </RNButton>
                )}
                <ActionStatusPopUpView
                    setActionPopupVisibility={setPopupVisibility}
                    visible={popupVisibility}
                    navigation={navigation}
                    employeeName={empName}
                    message={message}
                    userData={userData}
                    postLeaveStatus={postLeaveStatus}
                />
            </View>
        </View>
    );
};
export default DRsOnLeavesCard;

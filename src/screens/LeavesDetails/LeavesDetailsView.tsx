import React, { useState } from 'react';
import { SafeAreaView, ScrollView, View, FlatList } from 'react-native';
import { DataTable } from 'react-native-paper';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Ionicons from 'react-native-vector-icons/Ionicons';
import styles from './styles';
import { color } from '../../utils/constants/color';
import { stringText } from '../../utils/constants/stringsText';
import RNText from '../../component/RNText';
import LeavesData from '../../utils/constants/LeavedTestData.json';
import YearlyLeavedDetails from './YearlyLeavedDetails';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import Spinner from '../../component/Loader';
import { userLeavesDetailsType } from '.';

export type Props = {
    GoToEditServiceRequests: () => void;
    userLeavesDetails: userLeavesDetailsType;
    fetching: boolean;
    errorHandlerVisibility: boolean;
    errorHandlerMessage: string;
    errorHandlerClicked: (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => void;
};

const LeavesDetailsView = (props: Props) => {
    const {
        GoToEditServiceRequests,
        fetching,
        errorHandlerVisibility,
        errorHandlerMessage,
        errorHandlerClicked,
        userLeavesDetails
    } = props;

    const formatKey = (key: string) => {
        return key
            .replace(/([A-Z])/g, ' $1')
            .replace(/^./, (str) => str.toUpperCase());
    };

    const [isVisible, setIsVisible] = useState<string>('');

    const handelToggal = (year: string) =>
        setIsVisible(isVisible === year ? '' : year);

    const renderItem = ({ item }: { item: any }) => (
        <YearlyLeavedDetails
            handelToggal={handelToggal}
            isVisible={isVisible}
            leaveAccrualListData={item}
        />
    );

    return (
        <SafeAreaView style={styles.mainSafeAreaView}>
            <ScrollView showsVerticalScrollIndicator={false}>
                {userLeavesDetails && (
                    <View style={{ padding: 10 }}>
                        <DataTable
                            style={[
                                styles.tableWrapper,
                                styles.tableWrapperDesign
                            ]}
                        >
                            <View
                                style={styles.empDetailsTableValuesWrapper}
                            >
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {stringText.EmployeeID}:
                                    </RNText>
                                </View>
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {userLeavesDetails?.employeeId}
                                    </RNText>
                                </View>
                            </View>
                            <View
                                style={styles.empDetailsTableValuesWrapper}
                            >
                                <View
                                   style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {stringText.EmployeeName}:
                                    </RNText>
                                </View>
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {userLeavesDetails?.employeeName}
                                    </RNText>
                                </View>
                            </View>
                            <View
                                style={styles.empDetailsTableValuesWrapper}
                            >
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {stringText.Designation}:
                                    </RNText>
                                </View>
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {userLeavesDetails?.designation}
                                    </RNText>
                                </View>
                            </View>
                            <View
                                style={styles.empDetailsTableValuesWrapper}
                            >
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {stringText.AccrualType}:
                                    </RNText>
                                </View>
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {userLeavesDetails?.accrualType}
                                    </RNText>
                                </View>
                            </View>
                            <View
                                style={styles.empDetailsTableValuesWrapper}
                            >
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {stringText.TotalLeavesAllocated}:
                                    </RNText>
                                </View>
                                <View
                                   style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {userLeavesDetails?.totalLeaves}
                                    </RNText>
                                </View>
                            </View>
                            <View
                                style={styles.empDetailsTableValuesWrapper}
                            >
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {stringText.LeavesAvailed}:
                                    </RNText>
                                </View>
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {userLeavesDetails?.availedLeaves}
                                    </RNText>
                                </View>
                            </View>
                            <View
                                style={styles.empDetailsTableValuesWrapper}
                            >
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {stringText.LeaveBalance}:
                                    </RNText>
                                </View>
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {userLeavesDetails?.balanceLeaves}
                                    </RNText>
                                </View>
                            </View>
                            <View
                                style={styles.empDetailsTableValuesWrapper}
                            >
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {stringText.LeavesAllocatedinpreviousFY}:
                                    </RNText>
                                </View>
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {userLeavesDetails?.prevYearTotalLeaves}
                                    </RNText>
                                </View>
                            </View>
                            <View
                                style={styles.empDetailsTableValuesWrapper}
                            >
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {stringText.PreviousFYLeaveencashment}:
                                    </RNText>
                                </View>
                                <View
                                    style={styles.empDetailsTableValueTextWrapper}
                                >
                                    <RNText
                                        style={[styles.empDetailsTableValueText]}
                                    >
                                        {userLeavesDetails?.encashedLeaves}
                                    </RNText>
                                </View>
                            </View>
                        </DataTable>
                    </View>
                )}

                {userLeavesDetails?.leaveAccrualList && (
                    <FlatList
                        data={userLeavesDetails.leaveAccrualList}
                        renderItem={renderItem}
                        keyExtractor={(item, index) => index.toString()}
                    />
                )}
            </ScrollView>
            <Spinner animating={fetching} />
            <ErrorHandlerPopup
                visible={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
        </SafeAreaView>
    );
};

export default LeavesDetailsView;

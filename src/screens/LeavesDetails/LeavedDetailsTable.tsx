import React, { useState } from 'react';
import { ScrollView, View, FlatList } from 'react-native';
import styles from './styles';
import RNText from '../../component/RNText';
import { DataTable } from 'react-native-paper';
import { stringText } from '../../utils/constants/stringsText';
import RNButton from '../../component/RNButton';

const LeavedDetailsTable: React.FC<{
    LeavedDetails: [];
    title: string;
    total: number;
    totalTitle: string;
}> = ({ LeavedDetails, title, total, totalTitle }) => {
    const [isFullDescriptionShown, setIsFullDescriptionShown] =
        useState<string>('');

    const truncateText = (text: string, maxLength = 20) => {
        if (text.length <= maxLength) {
            return text;
        }
        return text.substring(0, maxLength) + '...';
    };

    const renderItem = ({ item }: { item: any }) => (
        <DataTable style={{ width: '100%' }}>
            <View style={styles.tableValuesWrapper}>
                {Object.entries(item)
                    .slice(0, 5)
                    .map(([key, value]) => (
                        <RNButton
                            ActiveOpacity={key.includes('desc') ? 0.6 : 1}
                            handleOnPress={() =>
                                key.includes('desc')
                                    ? setIsFullDescriptionShown(
                                        isFullDescriptionShown ==
                                            value.toString()
                                            ? ''
                                            : value.toString()
                                    )
                                    : null
                            }
                            key={key}
                            style={styles.tableValueTextWrapper}
                        >
                            <RNText
                                style={[
                                    styles.tableValueText,
                                    {
                                        textAlign:
                                            key.includes('Date') ||
                                                key.includes('count')
                                                ? 'center'
                                                : 'left'
                                    }
                                ]}
                            >
                                {key.includes('Date')
                                    ? new Date(value).toLocaleDateString(
                                        'en-GB'
                                    )
                                    : key.includes('desc') &&
                                        isFullDescriptionShown ==
                                        value.toString()
                                        ? value.toString()
                                        : truncateText(value.toString())}
                            </RNText>
                        </RNButton>
                    ))}
            </View>
        </DataTable>
    );

    return (
        <View style={styles.tableWrapper}>
            <RNText
                style={[
                    styles.tableTopTitleText,
                    styles.tableTopTitleHeadingText
                ]}
            >
                {title + ' (' + total + ')'}
            </RNText>
            <ScrollView
                horizontal
                showsHorizontalScrollIndicator={true}
                contentContainerStyle={{ flexGrow: 1 }}
                persistentScrollbar={true}
                indicatorStyle='default'
>
                <View>
                    <View style={styles.tableTitleWrapper}>
                        <RNText style={[styles.tableTitleText]}>
                            {stringText.LeaveType}
                        </RNText>
                        <RNText style={[styles.tableTitleText, styles.tableTitleTextAlignment]}>
                            {stringText.LeaveStartDate}
                        </RNText>
                        <RNText style={[styles.tableTitleText, styles.tableTitleTextAlignment]}>
                            {stringText.LeaveEndDate}
                        </RNText>
                        <RNText style={[styles.tableTitleText, styles.tableTitleTextAlignment]}>
                            {stringText.Count}
                        </RNText>
                        <RNText style={[styles.tableTitleText]}>
                            {stringText.Description}
                        </RNText>
                    </View>
                    <FlatList
                        data={LeavedDetails}
                        renderItem={renderItem}
                        keyExtractor={(item, index) => index.toString()}
                    />
                    <View style={[{ paddingVertical: 8 }, styles.tableValuesWrapper]}>
                        <RNText style={styles.tableTotalTitleText}>{totalTitle}</RNText>
                        <RNText style={styles.tableTotalCountText}>{total}</RNText>
                    </View>
                </View>
            </ScrollView>
        </View>
    );
};

export default LeavedDetailsTable;

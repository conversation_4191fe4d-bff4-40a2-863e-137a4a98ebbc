import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

const styles = StyleSheet.create({
    mainSafeAreaView: {
        // padding: 10,
        flex: 1
    },
    textHead: {
        color: color.GREY_COLOR,
        fontSize: 22,
        fontWeight: "700",
        fontFamily: 'Poppins-Regular',
        textAlign: "center",
        marginVertical: 10,
    },
    tableTopTitleTextWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 6,
        alignItems: 'center',
        flexWrap: 'wrap',
        columnGap: 10,
        paddingHorizontal: 16
    },
    tableTopTitleText: {
        color: color.GREY_COLOR,
        fontSize: 16,
        fontFamily: 'Poppins-Regular',
        width: '90%',

    },
    tableWrapper: {
        width: '100%',
        paddingBottom: 20,
        backgroundColor: color.WHITE,
        padding: 10,
        elevation: 4
    },
    tableWrapperDesign: {
        width: '100%',
        borderRadius: 6,
        marginVertical: 10
    },
    tableValuesWrapper: {
        flexDirection: 'row',
        gap: 40,
        borderBottomWidth: 2,
        borderColor: color.BACKGROUND_COLOR,
        paddingHorizontal: 10
    },
    empDetailsTableValuesWrapper: {
        flexDirection: 'row',
        gap: 10,
        borderBottomWidth: 2,
        borderColor: color.BACKGROUND_COLOR,
        paddingHorizontal: 10
    },
    tableTitleWrapper: {
        backgroundColor: color.ACCENT_BLUE,
        flexDirection: 'row',
        width: '100%',
        gap: 40,
        padding: 10
    },
    tableTitleText: {
        color: color.WHITE,
        fontSize: 14,
        fontWeight: '900',
        fontFamily: 'Poppins-Regular',
        width: 140
    },
    tableValueTextWrapper: {
        paddingVertical: 8,
    },
    empDetailsTableValueTextWrapper: {
        paddingVertical: 8,
        width: '50%',
        paddingRight: 5,
    },
    tableValueText: {
        color: color.GREY_COLOR,
        fontSize: 14,
        fontFamily: 'Poppins-Regular',
        flexWrap: 'wrap',
        width: 140
    },
    empDetailsTableValueText: {
        color: color.GREY_COLOR,
        fontSize: 14,
        fontFamily: 'Poppins-Regular',
        flexWrap: 'wrap',
        width: "100%",
    },
    tableTotalTitleText: {
        fontSize: 14,
        fontFamily: 'Poppins-Regular',
        flexWrap: 'wrap',
        width: 500
    },
    tableTotalCountText: {
        fontSize: 14,
        fontFamily: 'Poppins-Regular',
        flexWrap: 'wrap',
        width: 140,
        textAlign: 'center',
    },
    ButtonView: {
        position: 'absolute',
        right: 20,
        bottom: 20,
        height: 56,
        width: 56,
        resizeMode: 'cover',
        justifyContent: 'center',
        alignItems: 'center',
        backgroundColor: color.ACCENT_BLUE,
        borderRadius: 100,
    },
    tableTitleTextAlignment: {
        textAlign: 'center'
    },
    tableTopTitleHeadingText: {
        fontWeight: '700',
        marginBottom: 10,
        textAlign: 'center'
    },
});

export default styles;

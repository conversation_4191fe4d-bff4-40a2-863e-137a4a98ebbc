import React, { useEffect, useState } from 'react';
import { httpGet } from '../../utils/http';
import moment from 'moment';
import FoodView from './FoodView';
import { stringText } from '../../utils/constants/stringsText';
import { navigationStringText } from '../../utils/constants/navigationStringText';
import RNActivityIndicator from '../../component/Loader';
import { SafeAreaView } from 'react-native';

export type Props = {
    navigation: any
}

const FoodScreen = (props: Props) => {
    const { navigation } = props;
    const [fetching, setFetching] = useState<boolean>(false);
    const [datalunch, setDataLunch] = useState<Array<any>>([]);
    const [dataDinner, setDataDinner] = useState<Array<any>>([]);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] = useState<boolean>(false);
    const [dateArray, setDateArray] = useState<Date>(new Date());

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    useEffect(() => {
        loadData();
    }, [dateArray]);

    const loadData = () => {
        setFetching(true);
        const url = `/food_menu?from_date=${moment(dateArray).format('YYYY-MM-DD')}&to_date=${moment(dateArray).format('YYYY-MM-DD')}`;
        httpGet(url)
            .then((response: any) => {
                const data = JSON.parse(response)?.data;
                if (data) {
                    const LunchData = data[0]?.menu_launch.split(',');
                    const trimedLunchData = LunchData?.map((strLunch: string) => strLunch.trim());
                    const DinnerData = data[0]?.menu_dinner?.split(',');
                    const trimedDinnerData = DinnerData?.map((strDinner: string) => strDinner.trim());
                    setDataLunch(trimedLunchData);
                    setDataDinner(trimedDinnerData);
                }
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(true, err?.response?.data?.message || stringText.SomethingWentwrong);
            });
    };

    const GoToOrder = () => {
        navigation.navigate(navigationStringText.DinnerOrder);
    };

    const dayIncreament = () => {
        const nextDay = new Date(dateArray);
        nextDay.setDate(nextDay.getDate() + 1);
        setDateArray(nextDay);
    };

    const dayDecreament = () => {
        const prevDay = new Date(dateArray);
        prevDay.setDate(prevDay.getDate() - 1);
        setDateArray(prevDay);
    };

    const dateArraySplit = dateArray?.toDateString()?.split(' ');

    const getFullDayName = (day: string) => {
        var dayString: string = '';
        if (day === 'Mon') {
            dayString = 'Monday';
        } else if (day === 'Tue') {
            dayString = 'Tuesday';
        } else if (day === 'Wed') {
            dayString = 'Wednesday';
        } else if (day === 'Thu') {
            dayString = 'Thursday';
        } else if (day === 'Fri') {
            dayString = 'Friday';
        } else if (day === 'Sat') {
            dayString = 'Saturday';
        } else if (day === 'Sun') {
            dayString = 'Sunday';
        }
        return dayString;
    };

    const getFullMonthName = (month: string) => {
        var monthString: string = '';
        if (month === 'Jan') {
            monthString = 'January';
        } else if (month === 'Feb') {
            monthString = 'February';
        } else if (month === 'Mar') {
            monthString = 'March';
        } else if (month === 'Apr') {
            monthString = 'April';
        } else if (month === 'May') {
            monthString = 'May';
        } else if (month === 'Jun') {
            monthString = 'June';
        } else if (month === 'Jul') {
            monthString = 'July';
        } else if (month === 'Aug') {
            monthString = 'August';
        } else if (month === 'Sep') {
            monthString = 'September';
        } else if (month === 'Oct') {
            monthString = 'October';
        } else if (month === 'Nov') {
            monthString = 'Novembar';
        } else if (month === 'Dec') {
            monthString = 'December';
        }
        return monthString;
    };

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <FoodView
                dateArray={dateArray}
                dayDecreament={dayDecreament}
                getFullDayName={getFullDayName}
                dateArraySplit={dateArraySplit}
                getFullMonthName={getFullMonthName}
                dayIncreament={dayIncreament}
                datalunch={datalunch}
                dataDinner={dataDinner}
                fetching={fetching}
                GoToOrder={GoToOrder}
                errorHandlerVisibility={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
            <RNActivityIndicator animating={fetching} />
        </SafeAreaView>
    );
};

export default FoodScreen;

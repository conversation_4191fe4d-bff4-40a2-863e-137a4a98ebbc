import React from 'react';
import { SafeAreaView, ScrollView, View, ImageBackground } from 'react-native';
import { DataTable } from 'react-native-paper';
import MaterialCommunityIcons from 'react-native-vector-icons/MaterialCommunityIcons';
import Spinner from '../../component/Loader';
import { FlatList } from 'react-native-gesture-handler';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import styles from './styles';
import { color } from '../../utils/constants/color';
import { stringText } from '../../utils/constants/stringsText';
import RNText from '../../component/RNText';
import RNImage from '../../component/RNImage';
import LunchDinnerCard from './LunchDinnerCard';
import RNButton from '../../component/RNButton';
import imageConstant from '../../utils/constants/imageConstant';

let ListviewRef;

export type Props = {
    dateArray: Date;
    dayDecreament: () => void;
    getFullDayName: (day: string) => void;
    dateArraySplit: Array<string>;
    getFullMonthName: (month: string) => void;
    dayIncreament: () => void;
    datalunch: Array<any>;
    dataDinner: Array<any>;
    fetching: boolean;
    GoToOrder: () => void;
    errorHandlerVisibility: boolean;
    errorHandlerMessage: string;
    errorHandlerClicked: (errorHandlerVisibility: boolean, errorHandlerMessage: string) => void;
};

const FoodView = (props: Props) => {

    const { dateArray, dayDecreament, getFullDayName, dateArraySplit, getFullMonthName,
        dayIncreament, datalunch, dataDinner, fetching, GoToOrder, errorHandlerVisibility,
        errorHandlerMessage, errorHandlerClicked } = props;

    return (
        <SafeAreaView style={styles.mainSafeAreaView}>
            <ScrollView contentContainerStyle={styles.mainScrollView}>
                <View style={styles.mainViewstyles}>
                    <RNButton
                        disabled={
                            dateArray.toLocaleDateString() ===
                            new Date().toLocaleDateString()
                        }
                        handleOnPress={() => {
                            dayDecreament();
                        }}
                        style={styles.LeftIcon}
                    >
                        <MaterialCommunityIcons
                            name="chevron-left"
                            color={
                                dateArray.toLocaleDateString() ===
                                    new Date().toLocaleDateString()
                                    ? color.GREAYSCALE
                                    : color.DARK_BLUE
                            }
                            size={30}
                        />
                    </RNButton>
                    <View style={styles.FullDayTextView} >
                        <RNText
                            style={styles.FullDayText}
                        >
                            {getFullDayName(dateArraySplit?.[0])}
                        </RNText>
                        <View style={styles.FullMonthTextView}>
                            <RNText
                                style={styles.FullMonthText}
                            >
                                {getFullMonthName(dateArraySplit?.[1])}{' '}
                            </RNText>
                            <RNText
                                style={styles.DateArraySpilt}
                            >
                                {dateArraySplit?.[2]},{' '}
                            </RNText>
                            <RNText
                                style={styles.ArraySplitDate}
                            >
                                {dateArraySplit?.[3]}
                            </RNText>
                        </View>
                    </View>
                    <RNButton
                        handleOnPress={() => {
                            dayIncreament();
                        }}
                        style={styles.RightIcon}
                    >
                        <MaterialCommunityIcons
                            name="chevron-right"
                            color={color.DARK_BLUE}
                            size={30}
                        />
                    </RNButton>
                </View>

                {(datalunch?.length <= 0 || datalunch === undefined) &&
                    (dataDinner?.length <= 0 || dataDinner === undefined) &&
                    !fetching && (
                        <View
                            style={styles.FoodNotAvailableText}
                        >
                            <RNImage
                                source={imageConstant.FoodNotAvailable}
                                style={styles.FoodNotAvailable}
                            />
                        </View>
                    )}

                {datalunch?.length > 0 && (
                    <View style={styles.tableView}>
                        <DataTable>
                            <DataTable.Header style={styles.headerView}>
                                <DataTable.Title textStyle={styles.headerLunch}>
                                    {stringText.LunchTittle}
                                </DataTable.Title>
                            </DataTable.Header>
                            <ImageBackground
                                source={imageConstant.Lunch}
                                resizeMode="cover"
                            >
                                <FlatList
                                    renderItem={({ item }) =>
                                        <LunchDinnerCard
                                            itemData={item}
                                        />
                                    }
                                    data={datalunch}
                                    ref={(ref) => {
                                        ListviewRef = ref;
                                    }}
                                    keyExtractor={(item: any) => item.id}
                                />
                            </ImageBackground>
                        </DataTable>
                    </View>
                )}

                {dataDinner?.length > 0 && (
                    <View style={styles.tableView}>
                        <DataTable>
                            <DataTable.Header style={styles.headerDinnerView}>
                                <DataTable.Title
                                    textStyle={styles.headerDinner}
                                >
                                    {stringText.DinnerTittle}
                                </DataTable.Title>
                            </DataTable.Header>
                            <ImageBackground
                                source={imageConstant.Dinner}
                                resizeMode="cover"
                            >
                                <FlatList
                                    renderItem={({ item }) =>
                                        <LunchDinnerCard
                                            itemData={item}
                                            isDinnerCard={true}
                                        />
                                    }
                                    data={dataDinner}
                                    ref={(ref) => {
                                        ListviewRef = ref;
                                    }}
                                    keyExtractor={(item: any) => item.id}
                                />
                            </ImageBackground>
                        </DataTable>
                    </View>
                )}
            </ScrollView>
            <RNButton
                disabled={
                    ((datalunch?.length <= 0 || datalunch === undefined) &&
                        (dataDinner?.length <= 0 ||
                            dataDinner === undefined)) ||
                    dateArray.toLocaleDateString() !==
                    new Date().toLocaleDateString()
                }
                style={styles.ButtonView}
                handleOnPress={() => GoToOrder()}
            >
                <ImageBackground
                    source={
                        ((datalunch?.length <= 0 || datalunch === undefined) &&
                            (dataDinner?.length <= 0 ||
                                dataDinner === undefined)) ||
                            dateArray.toLocaleDateString() !==
                            new Date().toLocaleDateString()
                            ? imageConstant.GreyCircle
                            : imageConstant.Circle
                    }
                    style={styles.ImgButton}
                >
                    <RNImage
                        source={imageConstant.OrderPlace}
                        style={styles.plusImage}
                    />
                </ImageBackground>
            </RNButton>
            <Spinner animating={fetching} />
            <ErrorHandlerPopup
                visible={errorHandlerVisibility}
                errorHandlerMessage={errorHandlerMessage}
                errorHandlerClicked={errorHandlerClicked}
            />
        </SafeAreaView>
    );
};

export default FoodView;

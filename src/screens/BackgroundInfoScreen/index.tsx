import React, { useEffect, useState } from 'react';
import { SafeAreaView, View } from 'react-native';
import apiConstant from '../../utils/constants/apiConstant';
import BackgroundInfoView from './BackgroundInfoView';
import { styles } from './styles';
import RNActivityIndicator from '../../component/Loader';
import { httpGet } from '../../utils/http';
import { stringText } from '../../utils/constants/stringsText';

type BackgroundInfoScreen = {
    navigation: any;
};

const BackgroundInfoScreen: React.FC<BackgroundInfoScreen> = () => {
    const [data, setData] = useState<any>([]);
    const [qualificationSet, setQualificationSet] = useState<any>([]);
    const [qualificationSkill, setQualificationSkill] = useState([]);
    const [fetching, setFetching] = useState<boolean>(false);
    const [errorHandlerMessage, setErrorHandlerMessage] = useState<string>('');
    const [errorHandlerVisibility, setErrorHandlerVisibility] =
        useState<boolean>(false);

    useEffect(() => {
        getBackgroundInfo();
        getSkill();
        getSets();
    }, []);
    const getBackgroundInfo = () => {
        setFetching(true);
        httpGet(apiConstant.BACKGROUNDINFOGET)
            .then((response: any) => {
                setData(JSON.parse(response).data);
                setFetching(false);
            }) 
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(
                    true,
                    err?.response?.data?.message ||
                        stringText.SomethingWentwrong
                );
            });
    };
    const getSkill = () => {
        setFetching(true);
        httpGet(apiConstant.skill)
            .then((response: any) => {
                setQualificationSkill(JSON.parse(response).data);
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(
                    true,
                    err?.response?.data?.message ||
                        stringText.SomethingWentwrong
                );
            });
    };
    const getSets = () => {
        setFetching(true);
        httpGet(apiConstant.set)
            .then((response: any) => {
                setQualificationSet(JSON.parse(response).data);
                setFetching(false);
            })
            .catch((err) => {
                setFetching(false);
                errorHandlerClicked(
                    true,
                    err?.response?.data?.message ||
                        stringText.SomethingWentwrong
                );
            });
    };

    const errorHandlerClicked = (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => {
        setErrorHandlerVisibility(errorHandlerVisibility);
        setErrorHandlerMessage(errorHandlerMessage);
    };

    return (
        <SafeAreaView style={{ flex: 1 }}>
            <View style={styles.parentContainer}>
                <BackgroundInfoView
                    data={data}
                    qualificationSet={qualificationSet}
                    qualificationSkill={qualificationSkill}
                    errorHandlerClicked={errorHandlerClicked}
                    errorHandlerMessage={errorHandlerMessage}
                    errorHandlerVisibility={errorHandlerVisibility}
                    fetching={fetching}
                />
                <RNActivityIndicator animating={fetching} />
            </View>
        </SafeAreaView>
    );
};

export default BackgroundInfoScreen;

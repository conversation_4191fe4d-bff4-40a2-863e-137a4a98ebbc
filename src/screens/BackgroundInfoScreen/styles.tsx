import { StyleSheet } from 'react-native';
import { color } from '../../utils/constants/color';

export const styles = StyleSheet.create({
    parentContainer: {
        // backgroundColor: color.WHITE,
        paddingHorizontal: 10,
        borderRadius: 5,
        rowGap: 15,
        flex: 1
    },
    tableTopTitleTextWrapper: {
        flexDirection: 'row',
        justifyContent: 'space-between',
        padding: 6,
        alignItems: 'center',
        flexWrap: 'wrap',
        columnGap: 10,
        paddingHorizontal: 16,
        marginTop: 5,
        backgroundColor: color.WHITE,
        borderRadius: 6,
        borderTopLeftRadius: 6,
        borderTopRightRadius: 6,
    },
    mainContainer: {
        flex: 1,
        padding: 14,
        rowGap: 15
    },
    horizontalLine: {
        // height:1,
        backgroundColor: color.BLACK,
        marginVertical: 8,
    },
    seletedCard: {
        backgroundColor: color.WHITE,
        padding: 10,
        borderTopRightRadius: 0,
        borderTopLeftRadius: 0,
        borderBottomRightRadius: 5,
        borderBottomLeftRadius: 5,
        rowGap: 15,
        // elevation: 5,
        paddingVertical: 20
    },
    cardTitle: {
        color: color.BLACK,
        fontSize: 16,
        fontWeight: '600',
        paddingVertical: 8,
        width: "95%",
        paddingLeft: 0
    },
    lebelStyle: {
        marginLeft: 5,
        fontWeight: '400',
        fontSize: 14
    },
    infoField: {
        borderWidth: 1,
        borderColor: color.BLACK,
        padding: 10,
        borderRadius: 8
    },
    viewContainer: {
        flexDirection: 'row',
        alignItems: 'center'
    },
    rowGap: {
        rowGap: 5
    }
});
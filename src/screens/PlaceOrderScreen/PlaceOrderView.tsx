import React from 'react';
import { SafeAreaView, View } from 'react-native';
import Spinner from '../../component/Loader';
import ErrorHandlerPopup from '../../component/ErrorHandlerPopup';
import styles from './styles';
import { stringText } from '../../utils/constants/stringsText';
import RNText from '../../component/RNText';
import RNImage from '../../component/RNImage';
import { OrderStatus } from '../../utils/enumConstants';
import imageConstant from '../../utils/constants/imageConstant';

export type Props = {
    orderstatusCompleted: string;
    isblurQRcode: boolean;
    loader: boolean;
    food: string;
    errorHandlerVisibility: boolean;
    errorHandlerMessage: string;
    errorHandlerClicked: (
        errorHandlerVisibility: boolean,
        errorHandlerMessage: string
    ) => void;
};

const PlaceOrderView = (props: Props) => {
    const {
        orderstatusCompleted,
        isblurQRcode,
        loader,
        food,
        errorHandlerVisibility,
        errorHandlerMessage,
        errorHandlerClicked
    } = props;

    return (
        <>
            <SafeAreaView style={styles.mainView}>
                {orderstatusCompleted === OrderStatus.COMPLETED ||
                orderstatusCompleted === OrderStatus.CANCELLED ? (
                    <>
                        <View style={styles.HederView}>
                            <RNText style={styles.HederTittle}>
                                {stringText.OrderCompleted}
                            </RNText>
                        </View>
                    </>
                ) : (
                    <>
                        <RNText style={styles.textOrder}>
                            {stringText.ScanOrder}
                        </RNText>
                        <View style={styles.Img}>
                            {!isblurQRcode && <Spinner animating={loader} />}
                            {isblurQRcode ? (
                                <View />
                            ) : (
                                <RNImage
                                    source={
                                        isblurQRcode
                                            ? imageConstant.BlurQRCode
                                            : { uri: food }
                                    }
                                    style={styles.ImgView}
                                />
                            )}
                        </View>
                    </>
                )}
                <ErrorHandlerPopup
                    visible={errorHandlerVisibility}
                    errorHandlerMessage={errorHandlerMessage}
                    errorHandlerClicked={errorHandlerClicked}
                />
            </SafeAreaView>
        </>
    );
};

export default PlaceOrderView;

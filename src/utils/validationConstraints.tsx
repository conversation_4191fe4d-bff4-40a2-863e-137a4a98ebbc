import { stringText } from "./constants/stringsText";

export const validation: any = () => ({

    Email: {
        presence: {
            allowEmpty: false,
            message: 'Please enter email'
        },
        format: {
            pattern: /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            message: `${stringText.IncorrectEmail}`
        }
    },
})
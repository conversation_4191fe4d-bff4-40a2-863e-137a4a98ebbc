export default {
    LOGIN: '/api/login',
    ATTENDANCEIN: '/generate_attendance_qr',
    ATTENDANCEOUT: '/generate_attendance_qr',
    TUDIP_BIRTHDAY_ANNIVERSARY: `/users/birthday_anniversary`,
    SERVICEREQUESTCARD: `/service-requests`,
    BACKGROUNDINFOGET: `/users/background-info`,
    skill: '/users/qualification-skill-set',
    set: '/users/qualification-set',
    USERs: '/users/user',
    ASSET: `/assets`,
    BANK_INFO: `/bank-info`,
    BANK_INFO_COUNTRIES: `/users/countries`,
    BANK_INFO_STATE: `/users/states`,
    BANK_INFO_ACCOUNT_TYPE: `/bank-info/account-type`,
    HOLIDAY: `/holidays`,
    USER_INFO: `/users/user-info`,
    ATTENDANCE_TIMESHEET: `/attendance-timesheets`,
    ATTENDANCE: `/attendance`,
    BASIC_INFO: `users/basic-info`,
    MANAGER_VIEW: `/api1/Managerview`,
    LEAVE_TIMESHEET: '/leaves/leaveTimesheets',
    LEAVE_UNDERLINGS: `/leaves/underlings`,
    DRs_LIST: `/users/drs-list`,
    DEPARTMENT: `/department`,
    REQUEST_TYPE: `/type`,
    SERVICE_REQUEST: `service-request`,
    EMP_INFO: `/users/emp-info`,
    GET_ALL_EMP: `/users/getAllEmployees`,
    USER_ID: `/users/user-by-id`,
    DESIGNATION: `/users/designation`,
    DESIGNATION_BAND: `/users/designation-band`,
    SERVICE_AGREEMENT: `users/service-agreement`,
    ALL_USER: `/users`,
    FOOD_HISTORY: `/food_order_history`,
    LEAVES: `/leaves`,
    ATTENDANCE_LOGS:`/attendance_history_logs`,
    RCA:`/rca`,
    IDSR:`/idsr`,
    PlanForTheDay:`/planforday`,
    EDIT_TIMESHEET_DETAILS: `/idsr/report`,
    STATUS: `/status`,
    STATUS_TYPE: `/statusType`,   
    SUBMIT_IDSR: `/idsr`,
    MANDATE_TYPES: `/projects/mandatetype/index?page&pageSize&search`,
    PLAN_FOR_THE_DAY: `/planfortheday`,
    PROJECTS: `/project`,
    PLAN_FOR_THE_DAY_REPORT: `/planfortheday/report`
}
export const stringText = {
    EmailText: "Email",
    PasswordText: "Password",
    DashboardTittle: "Tudipversaries &",
    DashboardTittleBirthday: "Birthdays",
    AnniversaryGreeting: "Happy Tudipversary!",
    BirthdayGreeting: "Happy Birthday Tudipians!",
    HolidaysText: "Upcoming Holidays!",
    LunchTittle: "Lunch",
    DinnerTittle: "Dinner",
    DinnerHistory: "Dinner Order History",
    orderDate: "Order date",
    DataNotAvailable: "No Data Available",
    OrderCompleted: "Your order is already completed or cancelled.",
    ScanOrder: "Please scan code to place order",
    GenerateQRcode: "Generate QR code for",
    InText: "In",
    OutText: "Out",
    QRcodeText: "Get QR code here",
    LogoutTextPopUp: "Do you want to Logout?",
    NoText: "No",
    YesText: "Yes",
    EmpText: "Emp ID:",
    LogoutText: "Logout",
    LoginText: "Login",
    AttendanceHistory: "Your Attendance History",
    selectDate: "Please select date",
    SelectCorrectDate: "Please select the date in correct range.",
    SomethingWentwrong: "Something went wrong. Please try again later.",
    ConfirmLocation: "Please confirm you have enabled your device location.",
    SelectDate: "Search date",
    LastDate: "Last Date",
    EmailErrorMessage: "Please Enter Email",
    PasswordErrorMessage: "Please Enter Password",
    IncorrectEmail: "Incorrect email",
    DateText: "Date",
    InTime: "In Time",
    OutTime: "Out Time",
    TotalHrs: "Total Hrs",
    ViewLogs: "View Logs",
    OrderIDText: "Order ID",
    Status: "Status",
    CopyRightString: "Copyright © 2024 by XXXXX. \n All Rights Reserved.\n YYYYY.",
    TudipTechnologies: "Tudip Technologies.",
    PrivacyPolicy: "Privacy Policy.",
    MyRequest: "My Request",
    NewRequest: "New Request",
    SearchByTitle: "Search",
    Department: "Department",
    RequestType: "Request Type",
    Title: "Title",
    Description: "Description",
    Priority: "Priority",
    Create: "Create",
    LeaveAccrualReport: "Leave Accrual Report",
    SelectDepartment: "Select Department",
    DRsOnLeaveInSelectedMonth: "DRs On Leave In Selected Month",
    Search: "Search",
    SelectStatus: "Select Status",
    SelectMonth: "Select Month",
    EmployeeID: "Employee ID",
    EmployeeName: "Employee Name",
    LeaveStartDate: "Leave Start Date",
    LeaveEndDate: "Leave End Date",
    Action: "Action",
    AreYouSureWantToReject: "Are you sure you want to reject the",
    AreYouSureWantToAccept: "Are you sure you want to accept the",
    Leave: "leave?",
    Cancel: "Cancel",
    Ok: "Ok",
    SelectType: "Select Type",
    EnterTitle: "Enter Title",
    EnterShortDescription: "Enter Short Description",
    SelectPriority: "Select Priority",
    Save: "SAVE",
    AddComment: "Add Comment",
    LoginInWithGoogleText: "Login with Tudip Gmail",
    LeaveFor: "Leave For",
    StartDate: "Start Date",
    EndDate: "End Date",
    FilterStatus: "Status",
    FilterPriority: "Priority",
    Post: "Post",
    LaptopId: "LAPTOP ID",
    Ram: "RAM",
    HardDisk: "HARD DISK",
    Make: "MAKE",
    AssignedOn: "ASSIGNED ON",
    AaadharNumber: "Aadhar Number",
    PanNumber: "PAN Number",
    PassportNumber: "Passport Number",
    PfApplicable: "PF Applicable",
    PfNumber: "PF Number",
    UanNumber: "UAN Number",
    Designation: "Designation",
    DesignationBand: "Designation Band",
    Manager: "Manager",
    FirstWeekend: "First Weekend",
    SecondWeekend: "Second Weekend",
    HireData: "Hire Date",
    ExperiencewhileJoining: "Experience while Joining",
    ServiceAgreementPeriod: "Service Agreement Period",
    ServiceAgreementAmount: "Service Agreement Amount",
    UnderNotice: "Under Notice",
    NoticePeriod: "Notice Period",
    TypeOfRelieving: "Type Of Relieving",
    FirstPerformanceReview: "First Performance Review",
    IdCardIssued: "ID Card Issued",
    MediclaimIssued: "Mediclaim Issued",
    underPIP: "Under Performance Improvement Plan",
    UnderMetrnity: "Under Maternity",
    MediclaimNumber: "Mediclaim Number",
    TShirtSize: "T-Shirt Size",
    ReferredBy: "Referred By",
    ViewMore: "View More",
    Type: "Type",
    Addcomment: "Add comment",
    SelectEmployee: "Select Employee",
    ProjectGroup: "Project Group",
    ProjectGroupEmail: "Please enter email addresses separated by commas.",
    Amount: 'Amount',
    UploadFile: "Upload File",
    PleaseEnterProjectGroupSeparatedByCommas: "Please enter project group separated by commas.",
    QualificationOrSkillset: "Qualification Or Skillset",
    Graduated: "Graduated",
    PassingYear: "Passing Year",
    CollegeUniversity: "College/University",
    BranchSubject: "Branch/Subject",
    PercentageCgpa: "Percentage/CGPA",
    EmployeeBankInformation: "Employee Bank Information",
    EmployeeBankName: "Employee Bank Name",
    IFSCCode: "IFSC Code",
    BankBranchAddressNo: "Bank Branch Address No",
    BankBranchAddressNo2: "Bank Branch Address No2",
    BranchName: "Branch Name",
    EmployeeAccountNumber: "Employee Account Number",
    City: "City",
    State: "State",
    Country: "Country",
    AccountType: "Account Type",
    Attendance: "Attendance",
    Timesheet: "Timesheet",
    RCA: "RCA",
    Leaves: "Leaves",
    WorkStation: "Work Station",
    UpcomingLeave: "Upcoming Leave",
    CurrentProject: "Current Project",
    OfficeTiming: "Office Timing",
    OfficeLocation: "Office Location",
    EmpId: "Emp ID",
    Count: "Count",
    LeaveType: "Leave Type",
    AccrualType: "Accrual Type",
    TotalLeavesAllocated: "Total Leaves Allocated",
    LeavesAvailed: "Leaves Availed",
    LeaveBalance: "Leave Balance",
    Subject: "Subject",
    SUBJECT: "SUBJECT",
    TIMESPENTMINS: "TIME SPENT(MINS)",
    TOTALTIMESPENT: "Total time spent: ",
    MINS: " mins",
    LeavesAllocatedinpreviousFY: "Leaves Allocated in previous FY",
    PreviousFYLeaveencashment: "Previous FY Leave encashment",
    LeavesDetailsfortheFinancialYear: "Leaves Details for the Financial Year",
    Reportingtime: "Reporting time",
    Document:"One-To-One Document",
    OpenInNewTab:"Open In New Tab ",
    LateAttendance:"Late Attendance",
    DATE:"DATE",
    LATEBY:"LATE BY",
    ACTUALREPORTING:"ACTUAL REPORTING",
    REPORTINGTIME:"REPORTING TIME",
    NoAttendance:"No Attendance",
    UpdateAvailable:"Update available!",
    TodayHighlights: "Today's Highlights",
    BirthdayCount: "Birthday Count",
    Project:'Projects',
    DRs: 'DRs',
    Free:'Free',
    Additional:'Additional',
    Billable:'Billable',
    Unbillable:'Unbillable',
    DRsLeaveDetails:'DRs Leave Details',
    ServiceRequests:'Service Requests',
    Open:'Open',
    Closed:'Closed',
    InProgress:'In Progress',
    Reopen:'Reopen',
    notFixed:"Won't Fixed",
    Total:'Total',
    Assets:'Assets',
    AssetId:'Asset Id',
    Brand:'Brand',
    AssignedDate:'Assigned On',
    LeaveDetails:'Leave Details',
    Holiday:'Holiday',
    UpcomingHolidays:'Upcoming Holidays',
    TotalHolidays:'Total Holidays',
    YourInfo:'Your Info',
    Name:'Name',
    IdText:'Employee Id'
}
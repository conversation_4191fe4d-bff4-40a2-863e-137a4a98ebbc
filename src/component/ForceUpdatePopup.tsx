import React from 'react';
import styles from './Styles';
import { Linking, Platform, SafeAreaView, View } from 'react-native';
import RNText from './RNText';
import RNImage from './RNImage';
import VersionCheck from 'react-native-version-check';
import RNButton from './RNButton';
import imageConstant from '../utils/constants/imageConstant';
import { stringText } from '../utils/constants/stringsText';

const ForceUpdatePopup = () => {
    const handleUpdateClick = async () => {
        try {
            const storeUrl =
            Platform.OS=='ios'?
            await VersionCheck.getAppStoreUrl({
                appID: 6449922526
            })
            :
            await VersionCheck.getStoreUrl({
                packageName: 'com.kuberproject'
            });
            if (storeUrl) {
                await Linking.openURL(storeUrl);
            } else {
                console.error('Store URL is empty');
            }
        } catch (error) {
            console.error('Error opening URL:', error);
        }
    };
    return (
        <SafeAreaView style={styles.updateParentView}>
            <View style={styles.updateView}>
                <RNText style={styles.updateTitleText}>
                    {stringText.UpdateAvailable}
                </RNText>
                <RNImage
                    source={imageConstant.KuberFinalLogo}
                    style={{ width: '75%', aspectRatio: 1 }}
                    resizeMode="contain"
                />
                <RNText style={styles.updateMessageText}>
                    To use this app, download the latest version.
                </RNText>
                <RNButton
                    style={{ alignItems: 'center' }}
                    handleOnPress={handleUpdateClick}
                >
                    <RNText style={styles.updateButtonText}>UPDATE</RNText>
                </RNButton>
            </View>
        </SafeAreaView>
    );
};

export default ForceUpdatePopup;

import React, { FC } from 'react';
import { Modal, View } from "react-native";
// import CardView from "react-native-cardview";
import { Card } from '@rneui/themed';
import Feather from "react-native-vector-icons/Feather";
import { color } from '../utils/constants/color';
import RNText from './RNText';
import styles from './Styles';

type Props = {
    visible?: boolean,
    errorHandlerMessage: string,
    errorHandlerClicked: (errorHandlerVisibility: boolean, errorHandlerMessage: string) => void
}

const ErrorHandlerPopup: FC<Props> = (props) => {
    const { visible, errorHandlerMessage, errorHandlerClicked } = props;

    if (visible) {
        setTimeout(function () {
            errorHandlerClicked(false, '')
        }, 3000);
    }

    return (
        <Modal transparent={visible}
            visible={visible}
            animationType="fade"
        >
            <View
                style={styles.Errorstyle}>
               <Card wrapperStyle={{alignItems:'center'}} containerStyle={[styles.ErrorCardView, { elevation:4,padding:0,borderRadius:4,margin:0 }]}>
                <Feather name="alert-circle" color={color.DARK_RED} size={24} />
                    <RNText style={styles.ErrorHandlerText}>{errorHandlerMessage}</RNText>
                </Card>
            </View>
        </Modal>
    )
};

export default ErrorHandlerPopup;

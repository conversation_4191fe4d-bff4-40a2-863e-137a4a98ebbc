import React, { useState } from 'react';
import { View, Animated } from 'react-native';
import { Card } from '@rneui/themed';
import RNImage from './RNImage';
import RNText from './RNText';
import styles from './Styles';
import FontAwesome5
    from 'react-native-vector-icons/FontAwesome5';
import MaterialIcons
    from 'react-native-vector-icons/MaterialIcons';
import { navigationStringText } from '../utils/constants/navigationStringText';
import RNButton from './RNButton';



export type Props = {
    item: any;
    index: number;
    birthdayColorFinder?: any;
    isBirthdayCard: boolean;
    anniversaryColorFinder?: any;
    navigation:any
};

const GreetingCard = (props: Props) => {
    
    const { item, index, birthdayColorFinder, isBirthdayCard, anniversaryColorFinder,navigation } = props;
    const [isExpanded, setIsExpanded] = useState(false);
    const scaleValue = new Animated.Value(1);
    const cardHeight = 150;

    const handleCardPress = () => {
        Animated.timing(scaleValue, {
            toValue: isExpanded ? 1 : 2, // Expand the card to 110% of its size
            duration: 300, // Adjust the duration as needed
            useNativeDriver: false, // Add this line if you're not using the native driver
        }).start(() => {
            setIsExpanded(!isExpanded);
        });
    };
    

    return (
        <RNButton 
        ActiveOpacity={0.8} 
        handleOnPress={()=> navigation.navigate(navigationStringText.EmployeeDetails,{empId:item.id})} 
        style={[styles.viewText, { height: cardHeight }]}
        >

            <Card containerStyle={[styles.cardView, { height: cardHeight,elevation:4,padding:0,borderRadius:4,margin:0 }]}>
                <View style={styles.greetingCardContainer}>
                    <View style={styles.iconContainer}>
                        {isBirthdayCard ? (
                            <FontAwesome5
                                name="birthday-cake"
                                // color={color.DARK_BLUE}
                                size={25}
                                style={[
                                    styles.GrettingCardImage,
                                    {
                                        color: birthdayColorFinder(index)
                                    }
                                ]}
                            />
                                    ) : (
                            <MaterialIcons
                                name="celebration"
                                // color={color.DARK_BLUE}
                                size={25}
                                style={[
                                    styles.GrettingCardImage,
                                    { color: anniversaryColorFinder(index) }
                                ]}
                            />

                        )}
                    </View>
                    <RNImage source={{ uri: item?.profile_image }} style={styles.ImgPro}/>

                    <View style={styles.textContainer}>
                        <View
                            style={
                                isBirthdayCard
                                    ? [
                                          styles.cardTopRightViewBirthday,
                                          {
                                              backgroundColor:
                                                  birthdayColorFinder(index)
                                          }
                                      ]
                                    : [
                                          styles.cardTopRightView,
                                          {
                                              backgroundColor:
                                                  anniversaryColorFinder(index)
                                          }
                                      ]
                            }
                        >
                            
                            <RNText style={styles.cardTopRightText}>
                                {item?.date?.split(' ')?.[0]?.substring(0, 4)}{'\n'}{item?.date?.split(' ')?.[1]?.substring(0, 3)}
                            </RNText>
                        </View>
                    </View>
                </View>
                <View style={styles.firstNameAndMessageView}  >
                    <RNText numberOfLines={4} style={styles.cakeText}>
                        {item?.first_name} {item?.last_name}
                    </RNText>
                    <RNText numberOfLines={4} style={styles.cakeNameText}>
                        Happy {item?.message}!
                    </RNText>
                </View>
            </Card>

        </RNButton >

    );
};

export default GreetingCard;

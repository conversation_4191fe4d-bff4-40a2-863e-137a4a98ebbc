import React, { useState } from 'react';
import { View, Modal, TouchableWithoutFeedback } from 'react-native';
import RNImage from './RNImage';

export type Props = {
  imageLink:any,
  style : any
}


const ImageViewer: React.FC<Props> = (props) => {
  const {
    imageLink,
    style
  } = props;
  const [modalVisible, setModalVisible] = useState(false);

  const toggleModal = () => {
    setModalVisible(!modalVisible);
  };

  return (
    <View>
      <TouchableWithoutFeedback onPress={toggleModal}>
        <RNImage source={{ uri: imageLink }} style={style} />
      </TouchableWithoutFeedback>
      <Modal
        animationType="fade"
        transparent={true}
        visible={modalVisible}
        onRequestClose={toggleModal}
      >
        <TouchableWithoutFeedback onPress={toggleModal}>
          <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', backgroundColor:'rgba(0,0,0,0.9)' }}>
            <RNImage source={{ uri: imageLink }} resizeMode='contain' style={{ width: '90%', height:'90%' }} />
          </View>
        </TouchableWithoutFeedback>
      </Modal>
    </View>
  );
};

export default ImageViewer;

import { StyleSheet } from "react-native";
import { color } from '../utils/constants/color'

const styles = StyleSheet.create({
    mainView: {
        flex: 1,
        backgroundColor: color.OFF_WHITE
    },
    ImageBackTop: {
        height: 120,
        width: '100%',
        borderRadius: 8,
        marginTop: 12,
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center'
    },
    SecondImageBackTop: {
        height: 120,
        width: '100%',
        borderRadius: 8,
        justifyContent: 'center',
        alignItems: 'center',
        alignSelf: 'center'
    },
    textStyle: {
        fontSize: 19,
        color: color.GREY_COLOR,
        textAlign: 'center',
        fontFamily: 'Poppins-SemiBold'
    },
    textStyle1: {
        fontSize: 25,
        color: color.GREY_COLOR,
        textAlign: 'center',
        fontFamily: 'Poppins-Regular'
    },
    cardView: {
        alignSelf: 'center',
        backgroundColor: color.WHITE,
        justifyContent: 'center',
        paddingBottom: 11
    },
    viewText: {
        marginLeft: 12,
        marginBottom: 41,
        marginRight: 6,
    },
    greetingCardContainer: {
        flexDirection: 'row',
        // backgroundColor:'red',

    },
    iconContainer: {
        // position: 'absolute',
        top: 0,
        left: 0,
        // padding: 10,
        margin:10,
        // backgroundColor:'red'
    },
    textContainer: {
        flexDirection: 'row',
        top: -17,
        right:-22
        // alignItems: 'center',
        // paddingRight: 10, // Add padding as needed
    },
    cakeText: {
        fontSize: 12,
        color: color.GREY_COLOR,
        fontFamily: 'Poppins-SemiBold',
        marginTop: 2,
        textAlign: 'center',
        width: 200
    },
    cakeNameText: {
        textAlign: 'center',
        fontSize: 10,
        color: color.GREY_COLOR,
        fontFamily: 'Poppins-Regular',
        marginTop: 2,
        width: 200
    },
    cakeImage: {
        alignSelf: 'center',
        justifyContent: 'center',
        alignItems: 'center',
        marginTop: 15
    },
    cardTopRightView: {
        width: 36,
        height: 36,
        // backgroundColor: color.LIGHT_BLUE,
        borderTopRightRadius: 4,
        borderBottomRightRadius: 0,
        borderBottomLeftRadius:8,
        // justifyContent: 'center',
        // alignItems: 'center'
    },
    cardTopRightText: {
        fontSize: 11,
        fontFamily: 'Poppins-SemiBold',
        color: color.WHITE,
        textAlign: 'center',
        paddingTop:3
    },
    firstNameAndMessageView: {
        justifyContent: 'center',
        alignItems: 'center'
    },

    styleText: {
        left: 10,
        fontSize: 18,
        color: color.ACCENT_BLUE,
        marginBottom: 18,
        fontFamily: 'Poppins-Regular'
    },
    ImgPro: {
        height: 48,
        width: 48,
        borderWidth: 1,
        borderRadius: 100,
        alignSelf: 'center',
        alignItems:'center',
        borderColor: color.DARK_RED,
        marginLeft: 35,
        marginRight: 15,
        marginTop: 16,
        // backgroundColor:'red'
    },
    cardTopRightViewBirthday: {
        top: 0,
        right:0,
        width: 36,
        height: 36,
        // backgroundColor: "red",
        borderTopRightRadius: 4,
        borderBottomRightRadius: 0,
        borderBottomLeftRadius:8,
        // justifyContent: 'center',
        // alignItems: 'center',

    },
    Errorstyle: {
        backgroundColor: 'rgba(0,0,0,0)',
        flex: 1,
        flexDirection: 'column',
        justifyContent: 'flex-end',
        paddingBottom: 25
    },
    ErrorCardView: {
        alignSelf: 'center',
        backgroundColor: color.WHITE,
        width: '90%',
        justifyContent: 'center',
        alignItems: 'center',
        paddingTop: 9,
        paddingBottom: 12,
        paddingHorizontal: 9
    },
    GrettingCardImage: {
        top: -25,
        left: -5,
        // backgroundColor:'red',
        height:25,
        width:25
    },
    activityIndicatorContainer: {
        height: '100%',
        width: '100%',
        justifyContent: 'center',
        alignItems: 'center',
        position: 'absolute'
    },
    spinnerStyle: {
        alignItems: 'center',
        justifyContent: 'center',
        position: 'absolute',
        opacity: 0.8,
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
        backgroundColor: 'rgba(0,0,0,0)'
    },
    spinnerStyle1: {
        alignItems: 'center',
        justifyContent: 'center',
        top: 0,
        bottom: 0,
        left: 0,
        right: 0,
    },
    LogOutPopup: {
        backgroundColor: "rgba(0,0,0,0)",
        flex: 1,
        flexDirection: "column",
        justifyContent: "center",
    },
    LogOutPopUpCardView: {
        alignSelf: "center",
        backgroundColor: color.WHITE,
        width: "90%",
        justifyContent: "center",
        alignItems: "center",
        paddingTop: 9,
        paddingBottom: 12,
        paddingHorizontal: 9,
    },
    LogOutTextPopup: {
        marginTop: 9,
        color: color.GREY_COLOR,
        fontSize: 14,
        fontFamily: "Poppins-Regular",
    },
    LogOutPopUpButtonView: {
        flexDirection: "row",
        marginTop: 15
    },
    NoTextView: {
        flex: 1,
        backgroundColor: color.WHITE,
        justifyContent: "center",
        alignItems: "center",
        marginRight: 10,
        borderRadius: 4,
        paddingVertical: 7,
        borderColor: color.DARK_BLUE,
        borderWidth: 1,
    },
    NoTextStyles: {
        color: color.DARK_BLUE,
        fontSize: 13,
        fontFamily: "Poppins-Regular",
    },
    LogOutPopUpYesView: {
        flex: 1,
        backgroundColor: color.DARK_BLUE,
        justifyContent: "center",
        alignItems: "center",
        marginLeft: 10,
        borderRadius: 4,
        paddingVertical: 7,
    },
    LogOutTextStyles: {
        color: color.WHITE,
        fontSize: 13,
        fontFamily: "Poppins-Regular",
    },
    ErrorHandlerText: {
        marginTop: 9,
        color: color.GREY_COLOR,
        fontFamily: 'Poppins-Regular',
        fontSize: 12,
    },
    RNDrawerItemContainer: {
        flexDirection: 'row',
        alignItems: 'center',
    },
    RNDrawerTextStyle: {
        color: color.BLACK,
        fontSize: 15,
        fontFamily: 'Poppins-Regular',
    },

    // Search bar styles
    SearchServiceRequestSearchBar: {
        paddingHorizontal: 10,
        borderRadius: 5,
        flexDirection: 'row',
        alignItems: 'center',
        columnGap: 10,
        backgroundColor:color.WHITE,
        elevation: 4
    },
    ServiceRequestSearchText: {
        textAlign: 'left',
        fontSize: 15,
        fontFamily: 'Poppins-Regular',
        flex: 1,
        color: color.BLACK,
        paddingVertical:6,
        paddingTop:7
    },

    //inputWrapper styles
    inputWrapper:{
        borderWidth: 1,
        paddingHorizontal: 10,
        borderRadius: 5,
        borderColor: color.DARK_BLUE,
        flexDirection: 'row',
        alignItems: 'center',
        color:color.BLACK,
        columnGap: 10,
    },
    updateParentView: { 
        flex: 1, 
        backgroundColor: 'lightgray', 
        justifyContent: 'center',
        position:'absolute',
        zIndex:10,
        height:'100%',
        width:'100%'
    },
    updateView: { 
        backgroundColor: 'white', 
        width: '90%', 
        alignSelf: 'center', 
        alignItems: 'center', 
        borderRadius: 20, 
        padding:20 
    },
    updateTitleText: {
        textAlign: 'center',
        fontSize: 20,
        color: 'black',
        fontWeight: '500',
        paddingHorizontal: 45,
    },
    updateMessageText: {
        textAlign: 'center',
        fontSize: 18,
        color: 'black',
        paddingHorizontal: 20,
        paddingVertical: 5,
        borderRadius: 10,
        width: '90%'
    },
    updateButtonText: {
        textAlign: 'center',
        fontSize: 18,
        color: 'black',
        fontWeight: '500',
        paddingHorizontal: 45,
        backgroundColor: 'lightblue',
        paddingVertical: 10,
        borderRadius: 10,
        marginTop: 20,
        width: '80%'
    }
});

export default styles;
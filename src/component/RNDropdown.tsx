import React from 'react';
import { Dropdown} from "react-native-element-dropdown";

export type Props = {
    children: any,
    style?: any,
    data?: string[],
    placeholder?: string,
    maxHeight?: number,
    labelField?: any|undefined,
    valueField?: any,
    value?: string,
    onFocus?: () => void,
    onChange?: (value: string) => void
}

const RNDropdown: React.FC<Props> = (props) => {
    const {
        children,
        style,
        placeholder,
        maxHeight,
        labelField,
        valueField,
        value,
        onFocus,
        onChange,
        data
    } = props;

    const dataArray: string[] = data ? data : [];

    return (
        <Dropdown 
        {...props}
            onChange={() => onChange}
            labelField={labelField ?? undefined}
            data={dataArray}
            style={style} valueField={0} />
    );
};

export default RNDropdown;

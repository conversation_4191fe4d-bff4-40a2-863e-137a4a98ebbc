import { Platform, ToastAndroid } from 'react-native';
import Toast from 'react-native-root-toast';

export const showError = (massage: string) => {
    if (Platform.OS === 'ios') {
        Toast.show(massage, {
            position: Toast.positions.BOTTOM,
            shadow: true,
            animation: true,
            hideOnPress: true
        });
    } else {
        ToastAndroid.showWithGravity(
            massage,
            ToastAndroid.SHORT,
            ToastAndroid.CENTER
        );
    }
};

import { Platform, ToastAndroid } from 'react-native';
import Toast from 'react-native-root-toast';

export const successToast = (massage: string) => {
   
    if (Platform.OS === 'ios') {
        Toast.show(massage, {
            position: Toast.positions.BOTTOM,
            shadow: true,
            animation: true,
            hideOnPress: true
        });
    } else {
        ToastAndroid.show(
            massage,
            ToastAndroid.SHORT
        );
    }
};

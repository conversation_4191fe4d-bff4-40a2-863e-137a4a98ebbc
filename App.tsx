import 'react-native-gesture-handler';
import React, { useEffect, useState } from 'react';
import {
    SafeAreaView,
    StyleSheet,
    LogBox,
    StatusBar,
    View,
    Text,
    Platform
} from 'react-native';
import Navigation from './src/navigation/Navigation';
import SplashScreen from 'react-native-splash-screen';
import { color } from './src/utils/constants/color';
import { addEventListener } from '@react-native-community/netinfo';
import RNButton from './src/component/RNButton';
import RNText from './src/component/RNText';
import Spinner from './src/component/Loader';
import RNImage from './src/component/RNImage';
import ImageConstant from './src/utils/constants/imageConstant.tsx';

const App = () => {
    LogBox.ignoreAllLogs();
    useEffect(() => {
        SplashScreen.hide();
    }, []);

    const [isConnectedState, setIsConnectedState] = useState<boolean | null>(
        true
    );
    const [isLoading, setIsLoading] = useState(false);

    useEffect(() => {
        const unsubscribe = addEventListener((state) => {
            setIsConnectedState(state.isConnected && state.isInternetReachable);
        });
        return () => {
            unsubscribe();
        };
    }, []);

    const handelRefrech = () => {
        setIsLoading(true);
        setTimeout(() => {
            unsubscribe();
            setIsLoading(false);
        }, 1000);
        const unsubscribe = addEventListener((state) => {
            setIsConnectedState(state.isConnected);
        });
    };

    return (
        <SafeAreaView style={styles.mainView}>
            <StatusBar backgroundColor={color.WHITE} barStyle="dark-content" />

            {isConnectedState ? (
                <Navigation />
            ) : (
                <>
                    <View
                        style={{
                            flex: 1,
                            backgroundColor: color.BACKGROUND_COLOR,
                            alignItems: 'center',
                            justifyContent: 'center'
                        }}
                    >
                        <RNImage
                            source={ImageConstant.NoInternet}
                            style={{
                                backgroundColor: color.BACKGROUND_COLOR,
                                alignItems: 'center',
                                justifyContent: 'center',
                                width: 110,
                                height: 110
                            }}
                            resizeMode="cover"
                        />
                        {isLoading ? (
                            <Spinner animating={isLoading} />
                        ) : (
                            <>
                                <Text
                                    style={{
                                        fontSize: 20,
                                        color: color.BLACK
                                    }}
                                >
                                    No internet
                                </Text>
                                <RNButton
                                    style={{
                                        backgroundColor: color.DARK_BLUE,
                                        paddingVertical: 10,
                                        paddingHorizontal: 40,
                                        borderRadius: 10,
                                        marginTop: 10
                                    }}
                                    handleOnPress={() => handelRefrech()}
                                >
                                    <RNText
                                        style={{
                                            color: color.WHITE,
                                            fontSize: 18
                                        }}
                                    >
                                        Refresh
                                    </RNText>
                                </RNButton>
                            </>
                        )}
                    </View>
                </>
            )}
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    mainView: {
        flex: 1
    }
});

export default App;
